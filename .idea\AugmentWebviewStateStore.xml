<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;04e05587-b44c-4d19-9cfd-3617099ce8f0&quot;,&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;conversations&quot;:{&quot;04e05587-b44c-4d19-9cfd-3617099ce8f0&quot;:{&quot;id&quot;:&quot;04e05587-b44c-4d19-9cfd-3617099ce8f0&quot;,&quot;createdAtIso&quot;:&quot;2025-09-23T00:49:05.823Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-09-23T00:54:03.497Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;4999cbd1-6e19-435c-8b90-257b3805ff37&quot;,&quot;uuid&quot;:&quot;ea5f39d7-e2b3-4a83-a4dc-76bca5854869&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1758588545824,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;0a20eda4-16f1-460d-b38a-2c91f09571d2&quot;,&quot;timestamp&quot;:&quot;2025-09-23T00:54:03.512Z&quot;,&quot;request_message&quot;:&quot;软件架构设计文档\n项目名称: 软件定义工业互联网中粒子群驱动的流量精细化分类管控系统\n\n版本: 1.0\n\n\n1. 概述\n1.1 项目目标\n本文档旨在为《软件定义工业互联网中粒子群驱动的流量精细化分类管控系统》提供全面的架构设计与开发规划。 该系统是一款专注于解决工业互联网（IIoT）中普遍存在的\n\n架构僵化、流量分类无效、资源预留效率低下和网络性能预测困难四大核心问题的综合性管控平台 。 项目致力于提供一个基于软件定义理念的灵活弹性架构（SIFA） ，并集成先进的AI算法，实现流量的精细化分类、基于粒子群优化的资源动态编排以及精准的网络性能预测，最终形成“预测-反馈-优化”的闭环智能管控能力 。\n\n1.2 核心技术栈\n后端开发: Python \n\n核心算法:\n\n资源编排： 粒子群优化 （Particle Swarm Optimization， PSO）\n\n\n流量分类： 概率潜语义分析 （Probabilistic Latent Semantic Analysis， PLSA） \n\n\n\n性能预测: 图神经网络 (Graph Neural Network, GNN) \n\n\n\nGUI框架 : PyQt5  (用于构建桌面EXE应用)\n\n\n\n网络模拟与交互: Scapy (用于流量包分析), Mininet API (用于与网络模拟器交互) \n\n\n2. 系统架构设计\n系统采用分层模块化设计，确保高内聚、低耦合，便于未来功能的扩展与维护。\n\n2.1 总体架构图 (文本描述)\n+------------------------------------------------------+\n|                  表现层 (Presentation Layer)           |\n|  [GUI界面: 仪表盘, 策略配置, 结果可视化, 用户管理]   |\n+------------------------------------------------------+\n                         ^\n                         | (API 调用 / 信号槽)\n                         v\n+------------------------------------------------------+\n|                  业务逻辑层 (Business Logic Layer)       |\n|                                                      |\n|  +-----------------+  +----------------------------+ |\n|  |  SIFA核心控制器  |  |      用户与权限管理模块        | |\n|  | (CoreController) |  | (UserAuthManager)          | |\n|  +-------+---------+  +----------------------------+ |\n|          |                                           |\n| +--------v--------+ +--------v---------+ +--------v---------+\n| |  流量分类模块     | | 资源编排模块       | | 性能预测模块       |\n| | (Classifier)    | | (Orchestrator)   | | (Predictor)      |\n| | - PLSA Engine   | | - PSO Engine     | | - GNN Engine     |\n| +-----------------+ +------------------+ +------------------+\n+------------------------------------------------------+\n                         ^\n                         | (数据读写 / 指令下发)\n                         v\n+------------------------------------------------------+\n|                 基础设施层 (Infrastructure Layer)      |\n|                                                      |\n| +------------------+ +-----------------------------+ |\n| |   数据持久化模块   | |       网络接口与模拟模块        | |\n| | (Database)       | | (NetworkInterface)          | |\n| +------------------+ +-----------------------------+ |\n+------------------------------------------------------+\n2.2 模块划分\n表现层 (UI): 用户交互的入口，负责数据显示和操作指令的接收。\n\n业务逻辑层 (Core Logic): 系统的核心，处理所有业务逻辑。\n\n\nSIFA核心控制器: 系统的大脑，负责调度其他模块协同工作，执行服务链的构建与部署逻辑 。\n\n\n\n流量分类模块: 负责对捕获的流量进行高精度分类 。\n\n\n资源编排模块: 核心功能模块，使用粒子群算法为时间确定性流量进行资源预留和路径规划 。\n\n\n性能预测模块: 预测网络未来状态，为资源编排提供决策依据 。\n\n用户与权限管理模块: 负责用户账户的创建、登录、验证和权限控制。\n\n基础设施层 (Infrastructure): 提供底层支持。\n\n数据持久化模块: 负责与数据库交互，存储用户信息、策略配置、流量数据和分析结果。\n\n网络接口与模拟模块: 负责从真实网卡或网络模拟器（如Mininet）捕获和注入流量。\n\n3. 系统界面设计\n3.1 登录/注册界面\n组件: 用户名输入框、密码输入框、登录按钮、注册按钮、服务器地址配置。\n\n3.2 主仪表盘 （Dashboard）\n布局: 采用栅格化布局，模块可拖拽、自定义。\n\n核心组件:\n\n实时网络拓扑图: 可视化展示当前纳管的网络节点、链路状态和资源占用率。\n\n\n关键性能指标 (KPI) 看板: 实时显示平均时延、丢包率、吞吐量、用户请求接收率等核心指标 。\n\n\n流量分类实时饼图/柱状图: 展示当前网络中工业控制流量 vs. 非控制流量的比例和数量 。\n\n告警与事件日志: 滚动显示性能预测告警、资源瓶颈、策略执行失败等重要事件。\n\n3.3 流量分类与监控界面\n功能:\n\n分类模型管理: 支持上传/选择PLSA分类模型，显示模型准确率等信息。\n\n\n实时流量列表: 以表格形式实时刷新捕获到的流量，每行包含源/目的IP、协议、分类结果（如“工业控制-Modbus”、“非控制-视频流”）、FEC值等 。\n\n流量筛选与追溯: 提供筛选器，可根据IP、分类结果等条件过滤流量，并查看流量的详细解码信息。\n\n3.4 资源编排策略界面\n功能:\n\n策略配置:\n\n允许用户创建新的资源编排策略。\n\n目标流量选择: 通过筛选器（如分类为“工业控制”的流量）选择需要进行资源保障的目标流量。\n\nQoS目标设定: 设置时延上限、丢包率目标等。\n\n\nPSO参数配置: 提供高级选项，允许调整粒子群算法的惯性权重、学习因子等参数，以适应不同网络环境 。\n\n\n策略列表与状态: 展示所有已配置的策略及其运行状态（激活/暂停），并显示该策略下的资源拒绝率 。\n\n编排结果可视化: 在网络拓扑图上高亮显示为特定流量规划的路径和预留的资源。\n\n3.5 性能预测与分析界面\n功能:\n\n\n预测结果展示: 以折线图形式展示未来一段时间内关键节点或链路的时延、丢包率预测曲线 。\n\n历史数据对比: 支持将预测数据与历史真实数据进行对比，评估预测模型的准确性。\n\n瓶颈分析报告: 系统根据预测结果自动生成网络瓶颈分析报告，指出潜在的风险点。\n\n4. 类与函数设计 (Python伪代码风格)\n4.1 业务逻辑层 (core_logic)\nsifa_core.py\n\n\n\nclass CoreController:\n    \&quot;\&quot;\&quot;系统核心控制器，调度所有模块\&quot;\&quot;\&quot;\n    def __init__(self, ui_signal):\n        self.classifier = PLSAClassifier(\&quot;path/to/model.pkl\&quot;)\n        self.orchestrator = PSOOrchestrator()\n        self.predictor = GNNPredictor(\&quot;path/to/gnn_model.pth\&quot;)\n        self.network_interface = NetworkInterface(\&quot;eth0\&quot;)\n        self.ui_signal = ui_signal # 用于向UI发送信号\n\n    def start_monitoring(self):\n        \&quot;\&quot;\&quot;启动系统总监控循环\&quot;\&quot;\&quot;\n        self.network_interface.start_capture(self.process_packet_callback)\n\n    def process_packet_callback(self, packet_data):\n        \&quot;\&quot;\&quot;处理捕获到的每一个数据包/流\&quot;\&quot;\&quot;\n        # 1. 分类\n        flow = self.create_flow_from_packet(packet_data)\n        flow.category, flow.fec = self.classifier.predict(flow.features)\n        self.ui_signal.emit(\&quot;update_flow_list\&quot;, flow)\n\n        # 2. 检查是否需要编排\n        if self.is_deterministic_flow(flow):\n            self.trigger_orchestration(flow)\n\n    def trigger_orchestration(self, flow):\n        \&quot;\&quot;\&quot;触发资源编排\&quot;\&quot;\&quot;\n        network_state = self.get_current_network_state()\n        optimal_plan = self.orchestrator.run_pso_optimization(flow, network_state)\n        if optimal_plan:\n            self.network_interface.deploy_plan(optimal_plan)\n            self.ui_signal.emit(\&quot;update_topology_view\&quot;, optimal_plan)\n\n    def run_prediction_cycle(self):\n        \&quot;\&quot;\&quot;定时运行性能预测\&quot;\&quot;\&quot;\n        network_graph = self.get_network_as_graph()\n        predicted_kpis = self.predictor.predict(network_graph)\n        self.ui_signal.emit(\&quot;update_prediction_charts\&quot;, predicted_kpis)\n        # 将预测结果反馈给编排器\n        self.orchestrator.update_with_prediction(predicted_kpis)\ntraffic_classifier.py\n\n\n\nclass PLSAClassifier:\n    \&quot;\&quot;\&quot;基于PLSA的流量精细化分类器\&quot;\&quot;\&quot;\n    def __init__(self, model_path: str):\n        # 加载预训练的PLSA模型、特征-词映射规则等\n        self.model = self.load_model(model_path)\n\n    def train(self, training_data: list):\n        \&quot;\&quot;\&quot;训练并保存模型（离线功能）\&quot;\&quot;\&quot;\n        pass\n\n    def predict(self, flow_features: dict) -&gt; (str, float):\n        \&quot;\&quot;\&quot;\n        对单个流量进行分类预测\n        :param flow_features: 流量的统计特征\n        :return: (分类结果字符串, 流量膨胀系数FEC)\n        \&quot;\&quot;\&quot;\n        # 遵循论文方法：特征-词映射 -&gt; 计算FEC -&gt; 判断类别\n        # 该方法在处理加密和微小流量时表现优异 [cite: 314, 318]\n        # 最终分类精确度可达97.2% [cite: 318]\n        category = \&quot;Industrial Control\&quot; # or \&quot;Non-Control\&quot;\n        fec_score = 15.6 \n        return category, fec_score\nresource_orchestrator.py\n\n\n\nclass PSOOrchestrator:\n    \&quot;\&quot;\&quot;基于粒子群优化的资源动态编排器\&quot;\&quot;\&quot;\n    def __init__(self, pso_params: dict = None):\n        self.params = pso_params or self.default_params()\n\n    def run_pso_optimization(self, flow: object, network_state: object) -&gt; object:\n        \&quot;\&quot;\&quot;\n        为指定的时间确定性流量运行PSO算法，寻找最优资源分配方案\n        :param flow: 需要编排的流量对象\n        :param network_state: 当前网络状态（节点负载、链路带宽等）\n        :return: 最优编排计划（路径、预留资源等），失败则返回None\n        \&quot;\&quot;\&quot;\n        # 实现PSO算法，适应度函数综合考虑时延、资源占用等\n        # 目标是提升吞吐量，降低拒绝率 [cite: 319, 1639]\n        # 论文表明此方法可降低拒绝率85% \n        optimal_plan = \&quot;path:[A-&gt;C-&gt;D], bandwidth:10Mbps, timeslot:3\&quot;\n        return optimal_plan\n\n    def update_with_prediction(self, predicted_kpis: dict):\n        \&quot;\&quot;\&quot;根据性能预测结果，调整编排策略\&quot;\&quot;\&quot;\n        pass\nperformance_predictor.py\n\n\n\nclass GNNPredictor:\n    \&quot;\&quot;\&quot;基于GNN的网络性能预测器\&quot;\&quot;\&quot;\n    def __init__(self, model_path: str):\n        # 加载预训练的GNN模型\n        self.model = self.load_model(model_path)\n\n    def predict(self, network_graph: object) -&gt; dict:\n        \&quot;\&quot;\&quot;\n        输入当前网络状态的图表示，预测未来性能\n        :param network_graph: 包含节点和链路特征的网络图数据\n        :return: 预测的KPI字典，如{'latency': '5ms', 'packet_loss': '0.01%'}\n        \&quot;\&quot;\&quot;\n        # 该模型能精准预测时延和丢包率，绝对误差低于5% \n        # 预测结果用于实现闭环优化 [cite: 312]\n        kpis = {'latency': '5ms', 'packet_loss': '0.01%'}\n        return kpis\n4.2 基础设施层（基础设施）\ndatabase.py\n\n\n\nclass DatabaseManager:\n    \&quot;\&quot;\&quot;数据库管理类，封装所有SQL操作\&quot;\&quot;\&quot;\n    def connect(self): pass\n    def get_user(self, username: str) -&gt; object: pass\n    def save_flow_data(self, flow: object): pass\n    def save_policy(self, policy: object): pass\nnetwork_interface.py\n\n\n\nclass NetworkInterface:\n    \&quot;\&quot;\&quot;网络交互模块，负责抓包和下发指令\&quot;\&quot;\&quot;\n    def __init__(self, interface_name: str):\n        self.interface = interface_name\n\n    def start_capture(self, callback_func):\n        \&quot;\&quot;\&quot;使用Scapy或类似工具开始抓包，并用回调函数处理\&quot;\&quot;\&quot;\n        pass\n\n    def deploy_plan(self, plan: object):\n        \&quot;\&quot;\&quot;将编排计划转换为具体的网络指令（如OpenFlow流表）下发\&quot;\&quot;\&quot;\n        pass&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;}],&quot;feedbackStates&quot;:{&quot;temp-fe-0009caa9-7352-4ea5-8099-636b15d72f49&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;195b71ff-5e7b-4ceb-84e5-c04947ee14b7&quot;}}}" />
      </map>
    </option>
  </component>
</project>