<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="HttpUrlsUsage" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredUrls">
        <list>
          <option value="http://0.0.0.0" />
          <option value="http://127.0.0.1" />
          <option value="http://activemq.apache.org/schema/" />
          <option value="http://cxf.apache.org/schemas/" />
          <option value="http://java.sun.com/" />
          <option value="http://javafx.com/fxml" />
          <option value="http://javafx.com/javafx/" />
          <option value="http://json-schema.org/draft" />
          <option value="http://localhost" />
          <option value="http://maven.apache.org/POM/" />
          <option value="http://maven.apache.org/xsd/" />
          <option value="http://primefaces.org/ui" />
          <option value="http://schema.cloudfoundry.org/spring/" />
          <option value="http://schemas.xmlsoap.org/" />
          <option value="http://tiles.apache.org/" />
          <option value="http://www.baofeng.com" />
          <option value="http://www.fun.tv" />
          <option value="http://www.ibm.com/webservices/xsd" />
          <option value="http://www.jboss.com/xml/ns/" />
          <option value="http://www.jboss.org/j2ee/schema/" />
          <option value="http://www.springframework.org/schema/" />
          <option value="http://www.springframework.org/security/tags" />
          <option value="http://www.springframework.org/tags" />
          <option value="http://www.thymeleaf.org" />
          <option value="http://www.w3.org/" />
          <option value="http://xmlns.jcp.org/" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="29">
            <item index="0" class="java.lang.String" itemvalue="pyinstaller" />
            <item index="1" class="java.lang.String" itemvalue="protobuf" />
            <item index="2" class="java.lang.String" itemvalue="opencv-python" />
            <item index="3" class="java.lang.String" itemvalue="PyQt5-sip" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="mkl-random" />
            <item index="6" class="java.lang.String" itemvalue="torchvision" />
            <item index="7" class="java.lang.String" itemvalue="PyQtChart" />
            <item index="8" class="java.lang.String" itemvalue="pyinstaller-hooks-contrib" />
            <item index="9" class="java.lang.String" itemvalue="tensorboard" />
            <item index="10" class="java.lang.String" itemvalue="tensorboard-plugin-wit" />
            <item index="11" class="java.lang.String" itemvalue="PyQt5" />
            <item index="12" class="java.lang.String" itemvalue="grpcio" />
            <item index="13" class="java.lang.String" itemvalue="PyQtChart-Qt5" />
            <item index="14" class="java.lang.String" itemvalue="urllib3" />
            <item index="15" class="java.lang.String" itemvalue="GPUtil" />
            <item index="16" class="java.lang.String" itemvalue="tqdm" />
            <item index="17" class="java.lang.String" itemvalue="scikit-image" />
            <item index="18" class="java.lang.String" itemvalue="albumentations" />
            <item index="19" class="java.lang.String" itemvalue="opencv-contrib-python" />
            <item index="20" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="21" class="java.lang.String" itemvalue="PyYAML" />
            <item index="22" class="java.lang.String" itemvalue="matplotlib" />
            <item index="23" class="java.lang.String" itemvalue="numpy" />
            <item index="24" class="java.lang.String" itemvalue="Pillow" />
            <item index="25" class="java.lang.String" itemvalue="ultralytics" />
            <item index="26" class="java.lang.String" itemvalue="uWSGI" />
            <item index="27" class="java.lang.String" itemvalue="Django" />
            <item index="28" class="java.lang.String" itemvalue="django-taggit" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>