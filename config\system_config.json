{"system": {"name": "工业互联网流量精细化分类管控系统", "version": "1.0.0", "debug_mode": false, "log_level": "INFO", "max_log_files": 10, "log_rotation_size": "10MB"}, "database": {"type": "sqlite", "db_path": "data/system.db", "backup_enabled": true, "backup_interval": 3600, "max_backups": 5}, "network": {"default_interface": "auto", "capture_buffer_size": 65536, "packet_timeout": 1000, "max_packet_size": 1500, "promiscuous_mode": false}, "plsa": {"n_components": 10, "max_iter": 100, "learning_method": "batch", "batch_size": 128, "random_state": 42, "fec_threshold": 0.5, "classification_confidence": 0.8}, "pso": {"population_size": 50, "max_iterations": 100, "inertia_weight": 0.9, "cognitive_coefficient": 2.0, "social_coefficient": 2.0, "velocity_clamp": 0.5, "convergence_threshold": 1e-06}, "gnn": {"model_type": "GCN", "hidden_dim": 64, "num_layers": 3, "dropout": 0.1, "learning_rate": 0.001, "batch_size": 32, "epochs": 100, "early_stopping_patience": 10, "model_save_path": "models/gnn_model.pth"}, "ui": {"theme": "default", "language": "zh_CN", "window_size": {"width": 1200, "height": 800}, "auto_refresh_interval": 5000, "max_table_rows": 1000, "chart_update_interval": 2000}, "security": {"session_timeout": 3600, "max_login_attempts": 5, "password_min_length": 6, "password_complexity": true, "encryption_algorithm": "PBKDF2", "salt_length": 32}, "performance": {"max_concurrent_flows": 10000, "flow_timeout": 300, "prediction_cache_size": 1000, "orchestration_queue_size": 500, "worker_threads": 4}, "monitoring": {"enable_system_monitoring": true, "cpu_threshold": 80.0, "memory_threshold": 85.0, "disk_threshold": 90.0, "network_threshold": 80.0, "alert_interval": 300}, "protocols": {"supported_protocols": ["TCP", "UDP", "ICMP", "Modbus", "EtherNet/IP", "PROFINET", "OPC-UA"], "industrial_ports": [502, 44818, 34962, 34963, 34964, 4840, 102]}, "qos": {"default_latency_threshold": 50.0, "default_bandwidth_requirement": 10.0, "default_packet_loss_threshold": 1.0, "priority_levels": {"critical": 1, "high": 2, "medium": 3, "low": 4}}, "data_export": {"export_formats": ["csv", "json", "xml"], "max_export_records": 100000, "export_path": "exports/", "auto_cleanup_days": 30}}