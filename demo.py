#!/usr/bin/env python3
"""
工业互联网流量管控系统演示脚本
用于演示系统的核心功能
"""

import sys
import os
import time
import random
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.infrastructure.database.database_manager import DatabaseManager
from src.infrastructure.config.config_manager import ConfigManager
from src.business_logic.auth.user_auth_manager import UserAuthManager
from src.business_logic.classifier.plsa_classifier import PLSAClassifier
from src.business_logic.orchestrator.pso_orchestrator import PSOOrchestrator


def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_step(step, description):
    """打印步骤"""
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)


def demo_database_operations():
    """演示数据库操作"""
    print_step(1, "数据库管理演示")
    
    # 初始化数据库
    db_manager = DatabaseManager("demo_system.db")
    print("✓ 数据库初始化完成")
    
    # 创建测试用户
    success = db_manager.create_user(
        'demo_admin', 'hashed_password_123', 'admin', '<EMAIL>'
    )
    if success:
        print("✓ 创建演示用户成功")
    else:
        print("⚠ 用户可能已存在")
    
    # 查询用户
    user = db_manager.get_user('demo_admin')
    if user:
        print(f"✓ 查询用户: {user['username']} ({user['role']})")
    
    # 保存流量数据
    flow_data = {
        'src_ip': '*************',
        'dst_ip': '*************',
        'src_port': 502,
        'dst_port': 502,
        'protocol': 'TCP',
        'packet_size': 64,
        'classification': 'Industrial Control',
        'fec_score': 0.85,
        'is_deterministic': True
    }
    
    success = db_manager.save_flow_data(flow_data)
    if success:
        print("✓ 保存流量数据成功")
    
    db_manager.close()
    print("✓ 数据库连接关闭")


def demo_authentication():
    """演示认证系统"""
    print_step(2, "用户认证演示")

    # 初始化认证系统
    db_manager = DatabaseManager("demo_system.db")
    auth_manager = UserAuthManager(db_manager)

    # 直接创建操作员用户（使用数据库管理器）
    operator_hash = auth_manager._hash_password('demo123')
    success = db_manager.create_user(
        'demo_operator', operator_hash, 'operator', '<EMAIL>'
    )
    if success:
        print("✓ 创建演示操作员成功")
    else:
        print("⚠ 用户可能已存在")

    # 用户登录
    session_id = auth_manager.authenticate('demo_operator', 'demo123')
    if session_id:
        print(f"✓ 用户登录成功，会话ID: {session_id[:8]}...")

        # 验证会话
        session = auth_manager.validate_session(session_id)
        print(f"✓ 会话验证: {'有效' if session else '无效'}")

        # 检查权限
        from src.business_logic.auth.user_auth_manager import Permission
        has_permission = auth_manager.check_permission(session_id, Permission.VIEW_FLOWS)
        print(f"✓ 查看流量权限: {'有' if has_permission else '无'}")

        # 注销
        success = auth_manager.logout(session_id)
        print(f"✓ 用户注销: {'成功' if success else '失败'}")

    db_manager.close()


def demo_flow_classification():
    """演示流量分类"""
    print_step(3, "流量分类演示")
    
    classifier = PLSAClassifier()
    print("✓ PLSA分类器初始化完成")
    
    # 模拟不同类型的流量数据
    flow_samples = [
        {
            'name': 'Modbus工业控制流量',
            'data': {
                'src_ip': '************',
                'dst_ip': '************',
                'src_port': 502,
                'dst_port': 502,
                'protocol': 'TCP',
                'packet_size': 64,
                'payload': b'\x00\x01\x00\x00\x00\x06\x01\x03\x00\x00\x00\x01'
            }
        },
        {
            'name': 'EtherNet/IP工业通信',
            'data': {
                'src_ip': '************',
                'dst_ip': '************',
                'src_port': 44818,
                'dst_port': 44818,
                'protocol': 'TCP',
                'packet_size': 128
            }
        },
        {
            'name': '普通HTTP流量',
            'data': {
                'src_ip': '************',
                'dst_ip': '*******',
                'src_port': 12345,
                'dst_port': 80,
                'protocol': 'TCP',
                'packet_size': 1024
            }
        }
    ]
    
    for sample in flow_samples:
        print(f"\n分析 {sample['name']}:")
        
        # 提取特征
        features = classifier.extract_flow_features(sample['data'])
        print(f"  特征数量: {len(features)}")

        # 计算FEC值
        fec_score = classifier._calculate_fec(features)
        print(f"  FEC值: {fec_score:.4f}")

        # 分类结果
        classification, fec = classifier.predict(features)
        print(f"  分类结果: {classification}")
        print(f"  预测FEC: {fec:.4f}")


def demo_resource_orchestration():
    """演示资源编排"""
    print_step(4, "资源编排演示")
    
    orchestrator = PSOOrchestrator()
    print("✓ PSO编排器初始化完成")
    
    # 创建网络节点和链路
    from src.business_logic.orchestrator.pso_orchestrator import NetworkNode, NetworkLink

    nodes = [
        NetworkNode('Controller', 100.0, 8.0),
        NetworkNode('Switch1', 50.0, 4.0),
        NetworkNode('Switch2', 50.0, 4.0),
        NetworkNode('Device1', 20.0, 2.0),
        NetworkNode('Device2', 20.0, 2.0)
    ]

    links = [
        NetworkLink('link1', 'Controller', 'Switch1', 1000.0, 5.0),
        NetworkLink('link2', 'Controller', 'Switch2', 1000.0, 8.0),
        NetworkLink('link3', 'Switch1', 'Device1', 100.0, 2.0),
        NetworkLink('link4', 'Switch1', 'Switch2', 500.0, 10.0),
        NetworkLink('link5', 'Switch2', 'Device2', 100.0, 3.0)
    ]

    orchestrator.update_network_state(nodes, links)
    print("✓ 网络状态更新完成")
    
    # 创建流量请求
    from src.business_logic.orchestrator.pso_orchestrator import FlowRequirement

    flow_requests = [
        FlowRequirement(
            flow_id='critical_control_001',
            src_node='Controller',
            dst_node='Device1',
            bandwidth_requirement=50.0,
            latency_requirement=10.0,
            packet_loss_requirement=0.1
        ),
        FlowRequirement(
            flow_id='monitoring_data_002',
            src_node='Device2',
            dst_node='Controller',
            bandwidth_requirement=20.0,
            latency_requirement=50.0,
            packet_loss_requirement=1.0
        )
    ]

    for request in flow_requests:
        print(f"\n编排流量 {request.flow_id}:")

        result = orchestrator.run_pso_optimization(request)
        if result:
            path = ' -> '.join(result['path'])
            print(f"  最优路径: {path}")
            print(f"  总延迟: {result['total_latency']:.2f} ms")
            print(f"  适应度: {result['fitness_score']:.6f}")
        else:
            print("  编排失败：无法满足QoS要求")


def demo_system_integration():
    """演示系统集成"""
    print_step(5, "系统集成演示")
    
    print("模拟实时流量处理...")
    
    # 初始化组件
    db_manager = DatabaseManager("demo_system.db")
    classifier = PLSAClassifier()
    orchestrator = PSOOrchestrator()
    
    # 模拟实时流量
    for i in range(5):
        # 生成随机流量
        flow_data = {
            'src_ip': f"192.168.1.{random.randint(10, 100)}",
            'dst_ip': f"192.168.1.{random.randint(101, 200)}",
            'src_port': random.choice([502, 44818, 80, 443, 22]),
            'dst_port': random.choice([502, 44818, 80, 443, 22]),
            'protocol': 'TCP',
            'packet_size': random.randint(64, 1500),
            'timestamp': time.time()
        }
        
        # 分类流量
        features = classifier.extract_flow_features(flow_data)
        classification, fec_score = classifier.predict(features)
        is_deterministic = fec_score > 0.5  # 简化的确定性判断
        
        # 保存到数据库
        flow_data.update({
            'classification': classification,
            'fec_score': fec_score,
            'is_deterministic': is_deterministic
        })
        
        db_manager.save_flow_data(flow_data)
        
        print(f"  流量 {i+1}: {flow_data['src_ip']}:{flow_data['src_port']} -> "
              f"{flow_data['dst_ip']}:{flow_data['dst_port']} "
              f"[{classification}] FEC={fec_score:.3f}")
        
        time.sleep(0.5)
    
    db_manager.close()
    print("✓ 实时流量处理演示完成")


def cleanup_demo_files():
    """清理演示文件"""
    demo_files = ['demo_system.db']
    
    for file in demo_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✓ 清理文件: {file}")


def main():
    """主演示函数"""
    print_header("工业互联网流量精细化分类管控系统演示")
    print("本演示将展示系统的核心功能模块")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 演示各个模块
        demo_database_operations()
        demo_authentication()
        demo_flow_classification()
        demo_resource_orchestration()
        demo_system_integration()
        
        print_header("演示完成")
        print("✓ 所有功能模块演示成功")
        print("✓ 系统核心功能验证通过")
        
    except Exception as e:
        print(f"\n✗ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理演示文件
        print("\n清理演示文件...")
        cleanup_demo_files()
        print("演示结束")


if __name__ == "__main__":
    main()
