﻿# 工业互联网流量精细化分类管控系统依赖包

# GUI框架
PyQt5==5.15.9
PyQt5-Qt5==5.15.2
PyQt5-sip==12.12.2

# 科学计算和机器学习
numpy==1.24.3
scipy==1.10.1
scikit-learn==1.3.0
matplotlib==3.7.2

# 深度学习框架 (GNN预测)
torch==2.0.1
torch-geometric==2.3.1
torch-scatter==2.1.1
torch-sparse==0.6.17

# 网络数据包处理
scapy==2.5.0
netifaces==0.11.0

# 数据处理
pandas==2.0.3
sqlite3  # Python内置模块

# 配置文件处理
pyyaml==6.0.1

# 日志和调试
colorlog==6.7.0

# 系统监控
psutil==5.9.5

# 网络协议支持
pymodbus==3.4.1  # Modbus协议支持
pycomm3==1.2.14  # EtherNet/IP协议支持

# 数据序列化
pickle  # Python内置模块
json    # Python内置模块

# 时间处理
python-dateutil==2.8.2

# 加密和安全
cryptography==41.0.3
bcrypt==4.0.1

# 多线程和异步
threading  # Python内置模块
asyncio    # Python内置模块

# 文件和路径处理
pathlib  # Python内置模块
os       # Python内置模块

# 数学计算
math     # Python内置模块
random   # Python内置模块

# 开发和测试工具 (可选)
pytest==7.4.0
pytest-qt==4.2.0
pytest-cov==4.1.0

# 代码质量工具 (可选)
flake8==6.0.0
black==23.7.0

# 文档生成 (可选)
sphinx==7.1.2
sphinx-rtd-theme==1.3.0
