#!/usr/bin/env python3
"""
工业互联网流量精细化分类管控系统启动脚本
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def check_dependencies():
    """检查依赖包"""
    try:
        import PyQt5
        import numpy
        import sklearn
        import torch
        import scapy
        print("✓ 核心依赖包检查通过")
        return True
    except ImportError as e:
        print(f"✗ 依赖包检查失败: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def install_dependencies():
    """安装依赖包"""
    try:
        print("正在安装依赖包...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    directories = [
        "data",
        "logs", 
        "exports",
        "models",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✓ 目录结构检查完成")


def check_permissions():
    """检查权限"""
    try:
        # 检查网络抓包权限（Linux/Mac）
        if os.name == 'posix':
            if os.geteuid() != 0:
                print("⚠ 警告: 非root用户可能无法进行网络抓包")
                print("  建议使用sudo运行或配置网络权限")
        
        # 检查文件写入权限
        test_file = Path("test_write_permission.tmp")
        test_file.write_text("test")
        test_file.unlink()
        
        print("✓ 权限检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 权限检查失败: {e}")
        return False


def run_system(debug=False):
    """运行系统"""
    try:
        # 设置环境变量
        env = os.environ.copy()
        if debug:
            env['PYTHONPATH'] = os.getcwd()
            env['DEBUG'] = '1'
        
        # 启动主程序
        print("正在启动工业互联网流量管控系统...")
        print("=" * 50)
        
        subprocess.run([
            sys.executable, "src/main.py"
        ], env=env)
        
    except KeyboardInterrupt:
        print("\n系统被用户中断")
    except Exception as e:
        print(f"系统启动失败: {e}")


def run_tests():
    """运行测试"""
    try:
        print("正在运行系统测试...")
        subprocess.check_call([
            sys.executable, "-m", "pytest", "tests/", "-v"
        ])
        print("✓ 测试完成")
    except subprocess.CalledProcessError:
        print("✗ 测试失败")
    except FileNotFoundError:
        print("⚠ 未找到pytest，跳过测试")


def show_system_info():
    """显示系统信息"""
    print("工业互联网流量精细化分类管控系统")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {os.name}")
    print(f"工作目录: {os.getcwd()}")
    print(f"用户: {os.getenv('USER', os.getenv('USERNAME', 'unknown'))}")
    
    # 显示依赖包版本
    try:
        import PyQt5
        print(f"PyQt5版本: {PyQt5.Qt.PYQT_VERSION_STR}")
    except:
        pass
    
    try:
        import numpy
        print(f"NumPy版本: {numpy.__version__}")
    except:
        pass
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
    except:
        pass
    
    print("=" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="工业互联网流量精细化分类管控系统启动脚本"
    )
    
    parser.add_argument(
        "--install", 
        action="store_true",
        help="安装依赖包"
    )
    
    parser.add_argument(
        "--check", 
        action="store_true",
        help="检查系统环境"
    )
    
    parser.add_argument(
        "--test", 
        action="store_true",
        help="运行系统测试"
    )
    
    parser.add_argument(
        "--debug", 
        action="store_true",
        help="调试模式启动"
    )
    
    parser.add_argument(
        "--info", 
        action="store_true",
        help="显示系统信息"
    )
    
    args = parser.parse_args()
    
    # 显示系统信息
    if args.info:
        show_system_info()
        return
    
    # 安装依赖
    if args.install:
        if not install_dependencies():
            sys.exit(1)
        return
    
    # 检查环境
    if args.check:
        print("正在检查系统环境...")
        
        if not check_python_version():
            sys.exit(1)
        
        if not check_dependencies():
            sys.exit(1)
        
        create_directories()
        check_permissions()
        
        print("✓ 系统环境检查完成")
        return
    
    # 运行测试
    if args.test:
        run_tests()
        return
    
    # 默认启动系统
    print("正在进行启动前检查...")
    
    if not check_python_version():
        sys.exit(1)
    
    if not check_dependencies():
        print("尝试自动安装依赖包...")
        if not install_dependencies():
            sys.exit(1)
    
    create_directories()
    
    if not check_permissions():
        print("权限检查失败，但仍尝试启动系统...")
    
    # 启动系统
    run_system(debug=args.debug)


if __name__ == "__main__":
    main()
