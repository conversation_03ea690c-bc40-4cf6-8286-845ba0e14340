"""
用户权限管理模块
负责用户认证、权限控制和会话管理功能
"""

import hashlib
import secrets
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import json

from ...infrastructure.database.database_manager import DatabaseManager


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"
    GUEST = "guest"


class Permission(Enum):
    """权限枚举"""
    # 系统管理权限
    SYSTEM_ADMIN = "system_admin"
    USER_MANAGEMENT = "user_management"
    CONFIG_MANAGEMENT = "config_management"
    
    # 监控权限
    VIEW_DASHBOARD = "view_dashboard"
    VIEW_FLOWS = "view_flows"
    VIEW_TOPOLOGY = "view_topology"
    VIEW_PREDICTIONS = "view_predictions"
    
    # 操作权限
    START_MONITORING = "start_monitoring"
    STOP_MONITORING = "stop_monitoring"
    CREATE_POLICY = "create_policy"
    MODIFY_POLICY = "modify_policy"
    DELETE_POLICY = "delete_policy"
    
    # 数据权限
    EXPORT_DATA = "export_data"
    IMPORT_DATA = "import_data"
    DELETE_DATA = "delete_data"


@dataclass
class UserSession:
    """用户会话"""
    session_id: str
    user_id: int
    username: str
    role: UserRole
    login_time: float
    last_activity: float
    ip_address: str
    permissions: List[Permission]


class UserAuthManager:
    """用户认证管理器"""
    
    def __init__(self, database: DatabaseManager):
        """
        初始化用户认证管理器
        :param database: 数据库管理器
        """
        self.database = database
        self.logger = logging.getLogger(__name__)
        
        # 活跃会话
        self.active_sessions: Dict[str, UserSession] = {}
        
        # 会话超时时间（秒）
        self.session_timeout = 3600  # 1小时
        
        # 角色权限映射
        self.role_permissions = {
            UserRole.ADMIN: [
                Permission.SYSTEM_ADMIN,
                Permission.USER_MANAGEMENT,
                Permission.CONFIG_MANAGEMENT,
                Permission.VIEW_DASHBOARD,
                Permission.VIEW_FLOWS,
                Permission.VIEW_TOPOLOGY,
                Permission.VIEW_PREDICTIONS,
                Permission.START_MONITORING,
                Permission.STOP_MONITORING,
                Permission.CREATE_POLICY,
                Permission.MODIFY_POLICY,
                Permission.DELETE_POLICY,
                Permission.EXPORT_DATA,
                Permission.IMPORT_DATA,
                Permission.DELETE_DATA
            ],
            UserRole.OPERATOR: [
                Permission.VIEW_DASHBOARD,
                Permission.VIEW_FLOWS,
                Permission.VIEW_TOPOLOGY,
                Permission.VIEW_PREDICTIONS,
                Permission.START_MONITORING,
                Permission.STOP_MONITORING,
                Permission.CREATE_POLICY,
                Permission.MODIFY_POLICY,
                Permission.EXPORT_DATA
            ],
            UserRole.VIEWER: [
                Permission.VIEW_DASHBOARD,
                Permission.VIEW_FLOWS,
                Permission.VIEW_TOPOLOGY,
                Permission.VIEW_PREDICTIONS,
                Permission.EXPORT_DATA
            ],
            UserRole.GUEST: [
                Permission.VIEW_DASHBOARD
            ]
        }
        
        # 确保有默认管理员账户
        self._ensure_default_admin()
        
        self.logger.info("用户认证管理器初始化完成")
    
    def _ensure_default_admin(self):
        """确保存在默认管理员账户"""
        try:
            admin_user = self.database.get_user("admin")
            if not admin_user:
                # 创建默认管理员账户
                password_hash = self._hash_password("admin123")
                success = self.database.create_user(
                    username="admin",
                    password_hash=password_hash,
                    email="<EMAIL>",
                    role="admin"
                )
                if success:
                    self.logger.info("默认管理员账户创建成功")
                else:
                    self.logger.error("默认管理员账户创建失败")
        except Exception as e:
            self.logger.error(f"默认管理员账户检查失败: {e}")
    
    def _hash_password(self, password: str, salt: str = None) -> str:
        """
        密码哈希
        :param password: 明文密码
        :param salt: 盐值，如果为None则生成新的
        :return: 哈希后的密码
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # 使用PBKDF2进行密码哈希
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """
        验证密码
        :param password: 明文密码
        :param password_hash: 存储的密码哈希
        :return: 验证是否成功
        """
        try:
            salt, stored_hash = password_hash.split(':', 1)
            
            # 计算输入密码的哈希
            computed_hash = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt.encode('utf-8'),
                100000
            )
            
            return computed_hash.hex() == stored_hash
            
        except Exception as e:
            self.logger.error(f"密码验证失败: {e}")
            return False
    
    def authenticate(self, username: str, password: str, ip_address: str = "unknown") -> Optional[str]:
        """
        用户认证
        :param username: 用户名
        :param password: 密码
        :param ip_address: 客户端IP地址
        :return: 会话ID，认证失败返回None
        """
        try:
            # 获取用户信息
            user = self.database.get_user(username)
            if not user:
                self.logger.warning(f"用户不存在: {username}")
                return None
            
            # 检查用户是否激活
            if not user.get('is_active', True):
                self.logger.warning(f"用户已被禁用: {username}")
                return None
            
            # 验证密码
            if not self._verify_password(password, user['password_hash']):
                self.logger.warning(f"密码验证失败: {username}")
                return None
            
            # 创建会话
            session_id = secrets.token_urlsafe(32)
            user_role = UserRole(user['role'])
            permissions = self.role_permissions.get(user_role, [])
            
            session = UserSession(
                session_id=session_id,
                user_id=user['id'],
                username=username,
                role=user_role,
                login_time=time.time(),
                last_activity=time.time(),
                ip_address=ip_address,
                permissions=permissions
            )
            
            # 存储会话
            self.active_sessions[session_id] = session
            
            # 更新最后登录时间
            self.database.update_last_login(username)
            
            # 记录登录日志
            self.database.log_system_event(
                level="INFO",
                module="auth",
                message=f"用户登录成功: {username}",
                details=f"IP: {ip_address}",
                user_id=user['id']
            )
            
            self.logger.info(f"用户认证成功: {username}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"用户认证失败: {e}")
            return None
    
    def logout(self, session_id: str) -> bool:
        """
        用户登出
        :param session_id: 会话ID
        :return: 登出是否成功
        """
        try:
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                
                # 记录登出日志
                self.database.log_system_event(
                    level="INFO",
                    module="auth",
                    message=f"用户登出: {session.username}",
                    user_id=session.user_id
                )
                
                # 删除会话
                del self.active_sessions[session_id]
                
                self.logger.info(f"用户登出成功: {session.username}")
                return True
            else:
                self.logger.warning(f"会话不存在: {session_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"用户登出失败: {e}")
            return False
    
    def validate_session(self, session_id: str) -> Optional[UserSession]:
        """
        验证会话
        :param session_id: 会话ID
        :return: 用户会话，验证失败返回None
        """
        try:
            if session_id not in self.active_sessions:
                return None
            
            session = self.active_sessions[session_id]
            current_time = time.time()
            
            # 检查会话是否超时
            if current_time - session.last_activity > self.session_timeout:
                self.logger.info(f"会话超时: {session.username}")
                del self.active_sessions[session_id]
                return None
            
            # 更新最后活动时间
            session.last_activity = current_time
            
            return session
            
        except Exception as e:
            self.logger.error(f"会话验证失败: {e}")
            return None
    
    def check_permission(self, session_id: str, permission: Permission) -> bool:
        """
        检查权限
        :param session_id: 会话ID
        :param permission: 权限
        :return: 是否有权限
        """
        try:
            session = self.validate_session(session_id)
            if not session:
                return False
            
            return permission in session.permissions
            
        except Exception as e:
            self.logger.error(f"权限检查失败: {e}")
            return False
    
    def create_user(self, username: str, password: str, email: str, role: str, 
                   creator_session_id: str) -> bool:
        """
        创建用户
        :param username: 用户名
        :param password: 密码
        :param email: 邮箱
        :param role: 角色
        :param creator_session_id: 创建者会话ID
        :return: 创建是否成功
        """
        try:
            # 检查创建者权限
            if not self.check_permission(creator_session_id, Permission.USER_MANAGEMENT):
                self.logger.warning("用户创建权限不足")
                return False
            
            # 验证角色
            try:
                user_role = UserRole(role)
            except ValueError:
                self.logger.error(f"无效的用户角色: {role}")
                return False
            
            # 检查用户名是否已存在
            existing_user = self.database.get_user(username)
            if existing_user:
                self.logger.warning(f"用户名已存在: {username}")
                return False
            
            # 创建用户
            password_hash = self._hash_password(password)
            success = self.database.create_user(username, password_hash, email, role)
            
            if success:
                # 记录创建日志
                creator_session = self.active_sessions.get(creator_session_id)
                self.database.log_system_event(
                    level="INFO",
                    module="auth",
                    message=f"用户创建成功: {username}",
                    details=f"角色: {role}, 创建者: {creator_session.username if creator_session else 'unknown'}",
                    user_id=creator_session.user_id if creator_session else None
                )
                
                self.logger.info(f"用户创建成功: {username}")
                return True
            else:
                self.logger.error(f"用户创建失败: {username}")
                return False
                
        except Exception as e:
            self.logger.error(f"用户创建异常: {e}")
            return False
    
    def change_password(self, session_id: str, old_password: str, new_password: str) -> bool:
        """
        修改密码
        :param session_id: 会话ID
        :param old_password: 旧密码
        :param new_password: 新密码
        :return: 修改是否成功
        """
        try:
            session = self.validate_session(session_id)
            if not session:
                return False
            
            # 获取用户信息
            user = self.database.get_user(session.username)
            if not user:
                return False
            
            # 验证旧密码
            if not self._verify_password(old_password, user['password_hash']):
                self.logger.warning(f"旧密码验证失败: {session.username}")
                return False
            
            # 更新密码
            new_password_hash = self._hash_password(new_password)
            # 这里需要在数据库管理器中添加更新密码的方法
            # success = self.database.update_password(session.username, new_password_hash)
            
            # 记录密码修改日志
            self.database.log_system_event(
                level="INFO",
                module="auth",
                message=f"密码修改成功: {session.username}",
                user_id=session.user_id
            )
            
            self.logger.info(f"密码修改成功: {session.username}")
            return True
            
        except Exception as e:
            self.logger.error(f"密码修改失败: {e}")
            return False
    
    def get_active_sessions(self, admin_session_id: str) -> List[Dict[str, Any]]:
        """
        获取活跃会话列表
        :param admin_session_id: 管理员会话ID
        :return: 活跃会话列表
        """
        try:
            # 检查管理员权限
            if not self.check_permission(admin_session_id, Permission.USER_MANAGEMENT):
                return []
            
            sessions = []
            current_time = time.time()
            
            for session_id, session in self.active_sessions.items():
                sessions.append({
                    'session_id': session_id,
                    'username': session.username,
                    'role': session.role.value,
                    'login_time': session.login_time,
                    'last_activity': session.last_activity,
                    'ip_address': session.ip_address,
                    'duration': current_time - session.login_time
                })
            
            return sessions
            
        except Exception as e:
            self.logger.error(f"获取活跃会话失败: {e}")
            return []
    
    def force_logout(self, target_session_id: str, admin_session_id: str) -> bool:
        """
        强制登出用户
        :param target_session_id: 目标会话ID
        :param admin_session_id: 管理员会话ID
        :return: 操作是否成功
        """
        try:
            # 检查管理员权限
            if not self.check_permission(admin_session_id, Permission.USER_MANAGEMENT):
                return False
            
            if target_session_id in self.active_sessions:
                target_session = self.active_sessions[target_session_id]
                admin_session = self.active_sessions.get(admin_session_id)
                
                # 记录强制登出日志
                self.database.log_system_event(
                    level="WARNING",
                    module="auth",
                    message=f"强制登出用户: {target_session.username}",
                    details=f"操作者: {admin_session.username if admin_session else 'unknown'}",
                    user_id=admin_session.user_id if admin_session else None
                )
                
                # 删除会话
                del self.active_sessions[target_session_id]
                
                self.logger.info(f"强制登出成功: {target_session.username}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"强制登出失败: {e}")
            return False
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            current_time = time.time()
            expired_sessions = []
            
            for session_id, session in self.active_sessions.items():
                if current_time - session.last_activity > self.session_timeout:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                session = self.active_sessions[session_id]
                self.logger.info(f"清理过期会话: {session.username}")
                del self.active_sessions[session_id]
            
            if expired_sessions:
                self.logger.info(f"清理了 {len(expired_sessions)} 个过期会话")
                
        except Exception as e:
            self.logger.error(f"会话清理失败: {e}")
    
    def get_auth_stats(self) -> Dict[str, Any]:
        """获取认证统计信息"""
        try:
            current_time = time.time()
            
            # 统计各角色的活跃用户数
            role_counts = {}
            for session in self.active_sessions.values():
                role = session.role.value
                role_counts[role] = role_counts.get(role, 0) + 1
            
            return {
                'active_sessions': len(self.active_sessions),
                'session_timeout': self.session_timeout,
                'role_distribution': role_counts,
                'total_permissions': len(Permission),
                'available_roles': [role.value for role in UserRole]
            }
            
        except Exception as e:
            self.logger.error(f"认证统计获取失败: {e}")
            return {'error': str(e)}
