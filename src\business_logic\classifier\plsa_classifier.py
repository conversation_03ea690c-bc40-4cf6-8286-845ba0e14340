"""
基于PLSA的流量精细化分类器
实现概率潜语义分析算法，用于工业互联网流量的精确分类
"""

import numpy as np
import pickle
import json
import logging
import os
from typing import Dict, Any, Tuple, List, Optional
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.decomposition import LatentDirichletAllocation
import joblib


class PLSAClassifier:
    """基于PLSA的流量精细化分类器"""
    
    def __init__(self, model_path: str = None, feature_mapping_path: str = None):
        """
        初始化PLSA分类器
        :param model_path: 预训练模型路径
        :param feature_mapping_path: 特征映射文件路径
        """
        self.model_path = model_path
        self.feature_mapping_path = feature_mapping_path
        self.model = None
        self.vectorizer = None
        self.feature_mapping = {}
        self.classification_rules = {}
        self.logger = logging.getLogger(__name__)
        
        # 工业协议分类规则
        self.industrial_protocols = {
            502: 'Modbus',
            44818: 'EtherNet/IP', 
            2404: 'IEC 61850',
            20000: 'DNP3',
            47808: 'BACnet',
            1883: 'MQTT',
            5683: 'CoAP'
        }
        
        # 加载模型和配置
        if model_path:
            self.load_model(model_path)
        if feature_mapping_path:
            self.load_feature_mapping(feature_mapping_path)
        
        self._init_classification_rules()
    
    def _init_classification_rules(self):
        """初始化分类规则"""
        self.classification_rules = {
            'industrial_control': {
                'ports': list(self.industrial_protocols.keys()),
                'packet_size_range': (64, 1500),
                'fec_threshold': 10.0
            },
            'video_stream': {
                'ports': [554, 1935, 8080],
                'packet_size_range': (1000, 65535),
                'fec_threshold': 50.0
            },
            'file_transfer': {
                'ports': [21, 22, 80, 443],
                'packet_size_range': (1400, 65535),
                'fec_threshold': 30.0
            },
            'real_time_communication': {
                'ports': [5060, 5061, 1720],
                'packet_size_range': (200, 1400),
                'fec_threshold': 15.0
            }
        }
    
    def extract_flow_features(self, flow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从流量数据中提取特征
        :param flow_data: 流量数据字典
        :return: 提取的特征字典
        """
        features = {}
        
        try:
            # 基础特征
            features['packet_size'] = flow_data.get('packet_size', 0)
            features['protocol'] = flow_data.get('protocol', 'unknown')
            features['src_port'] = flow_data.get('src_port', 0)
            features['dst_port'] = flow_data.get('dst_port', 0)
            
            # 端口特征
            dst_port = features['dst_port']
            features['is_industrial_port'] = dst_port in self.industrial_protocols
            features['industrial_protocol'] = self.industrial_protocols.get(dst_port, 'none')
            
            # 数据包大小特征
            packet_size = features['packet_size']
            features['size_category'] = self._categorize_packet_size(packet_size)
            
            # 协议特征
            protocol = features['protocol']
            features['is_tcp'] = protocol == 'TCP'
            features['is_udp'] = protocol == 'UDP'
            
            # 时间特征（如果有）
            if 'timestamp' in flow_data:
                features['hour'] = int(flow_data['timestamp'] % 86400 // 3600)
                features['is_business_hour'] = 8 <= features['hour'] <= 18
            
            # 计算FEC (Flow Expansion Coefficient)
            features['fec'] = self._calculate_fec(features)
            
            return features
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return {}
    
    def _categorize_packet_size(self, size: int) -> str:
        """根据数据包大小分类"""
        if size < 64:
            return 'tiny'
        elif size < 200:
            return 'small'
        elif size < 1000:
            return 'medium'
        elif size < 1500:
            return 'large'
        else:
            return 'jumbo'
    
    def _calculate_fec(self, features: Dict[str, Any]) -> float:
        """
        计算流量膨胀系数 (Flow Expansion Coefficient)
        FEC是PLSA算法中的关键指标，用于量化流量特征的复杂度
        """
        try:
            fec = 1.0
            
            # 基于数据包大小的FEC计算
            packet_size = features.get('packet_size', 0)
            if packet_size > 0:
                # 归一化数据包大小
                normalized_size = min(packet_size / 1500.0, 1.0)
                fec *= (1 + normalized_size * 10)
            
            # 基于协议类型的FEC调整
            if features.get('is_industrial_port', False):
                fec *= 0.8  # 工业协议通常更规律，FEC较低
            
            # 基于端口的FEC调整
            dst_port = features.get('dst_port', 0)
            if dst_port < 1024:  # 系统端口
                fec *= 0.9
            elif dst_port > 49152:  # 动态端口
                fec *= 1.2
            
            # 基于协议的FEC调整
            if features.get('is_tcp', False):
                fec *= 1.1  # TCP协议复杂度较高
            elif features.get('is_udp', False):
                fec *= 0.9  # UDP协议相对简单
            
            return round(fec, 2)
            
        except Exception as e:
            self.logger.error(f"FEC计算失败: {e}")
            return 1.0
    
    def predict(self, flow_features: Dict[str, Any]) -> Tuple[str, float]:
        """
        对单个流量进行分类预测
        :param flow_features: 流量的统计特征
        :return: (分类结果字符串, 流量膨胀系数FEC)
        """
        try:
            # 提取特征
            features = self.extract_flow_features(flow_features)
            fec_score = features.get('fec', 1.0)
            
            # 基于规则的快速分类
            category = self._rule_based_classification(features)
            
            # 如果有训练好的PLSA模型，使用模型进行精确分类
            if self.model and category == 'unknown':
                category = self._model_based_classification(features)
            
            # 确定是否为工业控制流量
            if features.get('is_industrial_port', False):
                category = f"Industrial Control - {features.get('industrial_protocol', 'Unknown')}"
            elif category == 'unknown':
                category = "Non-Control - General"
            
            self.logger.debug(f"流量分类结果: {category}, FEC: {fec_score}")
            return category, fec_score
            
        except Exception as e:
            self.logger.error(f"流量分类失败: {e}")
            return "Unknown", 1.0
    
    def _rule_based_classification(self, features: Dict[str, Any]) -> str:
        """基于规则的分类"""
        try:
            dst_port = features.get('dst_port', 0)
            packet_size = features.get('packet_size', 0)
            fec = features.get('fec', 1.0)
            
            # 检查每个分类规则
            for category, rules in self.classification_rules.items():
                if dst_port in rules['ports']:
                    size_min, size_max = rules['packet_size_range']
                    if size_min <= packet_size <= size_max and fec <= rules['fec_threshold']:
                        return category
            
            return 'unknown'
            
        except Exception as e:
            self.logger.error(f"规则分类失败: {e}")
            return 'unknown'
    
    def _model_based_classification(self, features: Dict[str, Any]) -> str:
        """基于PLSA模型的分类"""
        try:
            if not self.model or not self.vectorizer:
                return 'unknown'
            
            # 将特征转换为文本表示
            feature_text = self._features_to_text(features)
            
            # 向量化
            feature_vector = self.vectorizer.transform([feature_text])
            
            # 预测
            topic_probs = self.model.transform(feature_vector)
            predicted_topic = np.argmax(topic_probs[0])
            
            # 映射到分类标签
            topic_mapping = {
                0: 'industrial_control',
                1: 'video_stream', 
                2: 'file_transfer',
                3: 'real_time_communication'
            }
            
            return topic_mapping.get(predicted_topic, 'unknown')
            
        except Exception as e:
            self.logger.error(f"模型分类失败: {e}")
            return 'unknown'
    
    def _features_to_text(self, features: Dict[str, Any]) -> str:
        """将特征转换为文本表示，用于PLSA处理"""
        text_parts = []
        
        # 协议信息
        protocol = features.get('protocol', 'unknown')
        text_parts.append(f"protocol_{protocol}")
        
        # 端口信息
        dst_port = features.get('dst_port', 0)
        text_parts.append(f"port_{dst_port}")
        
        # 大小类别
        size_category = features.get('size_category', 'unknown')
        text_parts.append(f"size_{size_category}")
        
        # 工业协议
        if features.get('is_industrial_port', False):
            industrial_protocol = features.get('industrial_protocol', 'unknown')
            text_parts.append(f"industrial_{industrial_protocol}")
        
        # 时间特征
        if features.get('is_business_hour', False):
            text_parts.append("business_hour")
        
        return ' '.join(text_parts)
    
    def train(self, training_data: List[Dict[str, Any]], labels: List[str]) -> bool:
        """
        训练PLSA模型
        :param training_data: 训练数据列表
        :param labels: 对应的标签列表
        :return: 训练是否成功
        """
        try:
            self.logger.info("开始训练PLSA模型...")
            
            # 提取特征并转换为文本
            feature_texts = []
            for data in training_data:
                features = self.extract_flow_features(data)
                text = self._features_to_text(features)
                feature_texts.append(text)
            
            # 创建词汇表
            self.vectorizer = CountVectorizer(max_features=1000, stop_words=None)
            feature_matrix = self.vectorizer.fit_transform(feature_texts)
            
            # 训练LDA模型（作为PLSA的近似）
            n_topics = len(set(labels))
            self.model = LatentDirichletAllocation(
                n_components=n_topics,
                random_state=42,
                max_iter=100
            )
            self.model.fit(feature_matrix)
            
            self.logger.info(f"PLSA模型训练完成，主题数: {n_topics}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            return False
    
    def save_model(self, model_path: str) -> bool:
        """保存训练好的模型"""
        try:
            if self.model and self.vectorizer:
                model_data = {
                    'model': self.model,
                    'vectorizer': self.vectorizer,
                    'classification_rules': self.classification_rules
                }
                joblib.dump(model_data, model_path)
                self.logger.info(f"模型保存成功: {model_path}")
                return True
            else:
                self.logger.warning("没有可保存的模型")
                return False
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, model_path: str) -> bool:
        """加载预训练模型"""
        try:
            if not model_path or not os.path.exists(model_path):
                self.logger.warning(f"模型文件不存在: {model_path}")
                return False
            
            model_data = joblib.load(model_path)
            self.model = model_data.get('model')
            self.vectorizer = model_data.get('vectorizer')
            
            if 'classification_rules' in model_data:
                self.classification_rules.update(model_data['classification_rules'])
            
            self.logger.info(f"模型加载成功: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
    
    def load_feature_mapping(self, mapping_path: str) -> bool:
        """加载特征映射配置"""
        try:
            if not mapping_path or not os.path.exists(mapping_path):
                self.logger.warning(f"特征映射文件不存在: {mapping_path}")
                return False
            
            with open(mapping_path, 'r', encoding='utf-8') as f:
                self.feature_mapping = json.load(f)
            
            self.logger.info(f"特征映射加载成功: {mapping_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"特征映射加载失败: {e}")
            return False
    
    def get_classification_stats(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        return {
            'model_loaded': self.model is not None,
            'vectorizer_loaded': self.vectorizer is not None,
            'industrial_protocols': len(self.industrial_protocols),
            'classification_rules': len(self.classification_rules),
            'feature_mapping_loaded': bool(self.feature_mapping)
        }
