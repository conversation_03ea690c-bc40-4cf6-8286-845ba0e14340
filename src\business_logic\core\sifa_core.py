"""
SIFA核心控制器
系统的大脑，负责调度所有模块协同工作，执行服务链的构建与部署逻辑
"""

import logging
import threading
import time
import queue
from typing import Dict, Any, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal
import json

from ..classifier.plsa_classifier import PLSAClassifier
from ..orchestrator.pso_orchestrator import PSOOrchestrator, FlowRequirement, NetworkNode, NetworkLink
from ..predictor.gnn_predictor import GNNPredictor
from ...infrastructure.network.network_interface import NetworkInterface, NetworkFlow
from ...infrastructure.database.database_manager import DatabaseManager
from ...infrastructure.config.config_manager import ConfigManager


class CoreController(QObject):
    """系统核心控制器，调度所有模块"""
    
    # PyQt信号，用于与UI通信
    flow_detected = pyqtSignal(dict)  # 检测到新流量
    topology_updated = pyqtSignal(dict)  # 拓扑更新
    prediction_updated = pyqtSignal(dict)  # 预测结果更新
    orchestration_completed = pyqtSignal(dict)  # 编排完成
    system_alert = pyqtSignal(str, str)  # 系统告警 (level, message)
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        初始化核心控制器
        :param config_manager: 配置管理器
        """
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager or ConfigManager()
        
        # 系统状态
        self.is_running = False
        self.monitoring_thread = None
        self.prediction_thread = None
        
        # 核心模块
        self.classifier = None
        self.orchestrator = None
        self.predictor = None
        self.network_interface = None
        self.database = None
        
        # 流量处理队列
        self.flow_queue = queue.Queue(maxsize=10000)
        self.processing_thread = None
        
        # 系统统计
        self.stats = {
            'total_flows': 0,
            'industrial_flows': 0,
            'orchestrated_flows': 0,
            'prediction_count': 0,
            'start_time': None,
            'last_update': None
        }
        
        # 网络状态缓存
        self.network_state = {
            'nodes': {},
            'links': {},
            'topology': {}
        }
        
        # 初始化模块
        self._init_modules()
        
        self.logger.info("SIFA核心控制器初始化完成")
    
    def _init_modules(self):
        """初始化各个核心模块"""
        try:
            # 初始化数据库
            db_config = self.config_manager.get_database_config()
            self.database = DatabaseManager(db_config.db_path)
            
            # 初始化分类器
            plsa_config = self.config_manager.get_plsa_config()
            self.classifier = PLSAClassifier(
                model_path=plsa_config.model_path,
                feature_mapping_path=plsa_config.feature_mapping_path
            )
            
            # 初始化编排器
            pso_config = self.config_manager.get_pso_config()
            pso_params = {
                'population_size': pso_config.population_size,
                'max_iterations': pso_config.max_iterations,
                'inertia_weight': pso_config.inertia_weight,
                'cognitive_coefficient': pso_config.cognitive_coefficient,
                'social_coefficient': pso_config.social_coefficient,
                'convergence_threshold': pso_config.convergence_threshold
            }
            self.orchestrator = PSOOrchestrator(pso_params)
            
            # 初始化预测器
            gnn_config = self.config_manager.get_gnn_config()
            self.predictor = GNNPredictor(model_path=gnn_config.model_path)
            
            # 初始化网络接口
            network_config = self.config_manager.get_network_config()
            interface_name = network_config.default_interface if network_config.default_interface != "auto" else None
            self.network_interface = NetworkInterface(interface_name)
            
            self.logger.info("所有核心模块初始化完成")
            
        except Exception as e:
            self.logger.error(f"模块初始化失败: {e}")
            raise
    
    def start_monitoring(self):
        """启动系统总监控循环"""
        if self.is_running:
            self.logger.warning("系统已在运行中")
            return
        
        try:
            self.is_running = True
            self.stats['start_time'] = time.time()
            
            # 启动流量处理线程
            self.processing_thread = threading.Thread(
                target=self._flow_processing_loop,
                daemon=True
            )
            self.processing_thread.start()
            
            # 启动网络监控
            network_config = self.config_manager.get_network_config()
            self.network_interface.start_capture(
                callback_func=self._process_packet_callback,
                filter_str=network_config.capture_filter
            )
            
            # 启动预测循环
            self.prediction_thread = threading.Thread(
                target=self._prediction_loop,
                daemon=True
            )
            self.prediction_thread.start()
            
            self.logger.info("系统监控已启动")
            self.system_alert.emit("INFO", "系统监控已启动")
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            self.system_alert.emit("ERROR", f"系统启动失败: {e}")
            self.is_running = False
    
    def stop_monitoring(self):
        """停止系统监控"""
        if not self.is_running:
            return
        
        try:
            self.is_running = False
            
            # 停止网络捕获
            if self.network_interface:
                self.network_interface.stop_capture()
            
            # 等待线程结束
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=5)
            
            if self.prediction_thread and self.prediction_thread.is_alive():
                self.prediction_thread.join(timeout=5)
            
            self.logger.info("系统监控已停止")
            self.system_alert.emit("INFO", "系统监控已停止")
            
        except Exception as e:
            self.logger.error(f"系统停止失败: {e}")
    
    def _process_packet_callback(self, flow: NetworkFlow):
        """处理捕获到的每一个数据包/流"""
        try:
            # 将流量放入处理队列
            if not self.flow_queue.full():
                self.flow_queue.put(flow, block=False)
            else:
                self.logger.warning("流量处理队列已满，丢弃数据包")
                
        except Exception as e:
            self.logger.error(f"数据包回调处理失败: {e}")
    
    def _flow_processing_loop(self):
        """流量处理循环"""
        while self.is_running:
            try:
                # 从队列获取流量
                flow = self.flow_queue.get(timeout=1)
                
                # 处理流量
                self._process_flow(flow)
                
                # 更新统计
                self.stats['total_flows'] += 1
                self.stats['last_update'] = time.time()
                
                self.flow_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"流量处理循环异常: {e}")
    
    def _process_flow(self, flow: NetworkFlow):
        """处理单个流量"""
        try:
            # 1. 流量分类
            flow_dict = flow.to_dict()
            classification, fec_score = self.classifier.predict(flow_dict)
            
            # 更新流量信息
            flow.classification = classification
            flow.fec_score = fec_score
            
            # 判断是否为工业控制流量
            if "Industrial Control" in classification:
                flow.is_deterministic = True
                self.stats['industrial_flows'] += 1
            
            # 发送流量检测信号
            self.flow_detected.emit(flow.to_dict())
            
            # 2. 保存流量数据
            if self.database:
                self.database.save_flow_data(flow.to_dict())
            
            # 3. 检查是否需要编排
            if flow.is_deterministic:
                self._trigger_orchestration(flow)
            
        except Exception as e:
            self.logger.error(f"流量处理失败: {e}")
    
    def _trigger_orchestration(self, flow: NetworkFlow):
        """触发资源编排"""
        try:
            # 创建流量需求
            flow_requirement = FlowRequirement(
                flow_id=f"flow_{int(time.time())}_{flow.src_ip}_{flow.dst_ip}",
                src_node=flow.src_ip,
                dst_node=flow.dst_ip,
                bandwidth_requirement=max(1.0, flow.packet_size * 8 / 1000),  # 估算带宽需求
                latency_requirement=50.0,  # 默认50ms延迟要求
                packet_loss_requirement=0.01,  # 默认1%丢包率要求
                priority=5 if "Industrial Control" in flow.classification else 3
            )
            
            # 获取当前网络状态
            network_state = self._get_current_network_state()
            
            # 运行PSO优化
            optimal_plan = self.orchestrator.run_pso_optimization(flow_requirement, network_state)
            
            if optimal_plan:
                # 部署编排计划
                success = self.network_interface.deploy_plan(optimal_plan)
                
                if success:
                    self.stats['orchestrated_flows'] += 1
                    self.orchestration_completed.emit(optimal_plan)
                    self.logger.info(f"编排计划部署成功: {optimal_plan['flow_id']}")
                else:
                    self.logger.warning(f"编排计划部署失败: {optimal_plan['flow_id']}")
            else:
                self.logger.warning(f"未找到可行的编排方案: {flow_requirement.flow_id}")
                
        except Exception as e:
            self.logger.error(f"资源编排触发失败: {e}")
    
    def _get_current_network_state(self) -> Dict[str, Any]:
        """获取当前网络状态"""
        try:
            # 这里应该从实际网络设备获取状态
            # 目前返回模拟数据
            
            # 模拟网络节点
            nodes = [
                NetworkNode("node_1", 100.0, 8192.0, 30.0, 2048.0),
                NetworkNode("node_2", 100.0, 8192.0, 45.0, 3072.0),
                NetworkNode("node_3", 100.0, 8192.0, 25.0, 1536.0),
            ]
            
            # 模拟网络链路
            links = [
                NetworkLink("link_1", "node_1", "node_2", 1000.0, 5.0, 200.0),
                NetworkLink("link_2", "node_2", "node_3", 1000.0, 8.0, 150.0),
                NetworkLink("link_3", "node_1", "node_3", 1000.0, 12.0, 300.0),
            ]
            
            # 更新编排器的网络状态
            self.orchestrator.update_network_state(nodes, links)
            
            return {
                'nodes': [node.__dict__ for node in nodes],
                'links': [link.__dict__ for link in links],
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"网络状态获取失败: {e}")
            return {}
    
    def _prediction_loop(self):
        """性能预测循环"""
        gnn_config = self.config_manager.get_gnn_config()
        prediction_interval = gnn_config.update_interval
        
        while self.is_running:
            try:
                # 运行性能预测
                self._run_prediction_cycle()
                
                # 等待下一次预测
                time.sleep(prediction_interval)
                
            except Exception as e:
                self.logger.error(f"预测循环异常: {e}")
                time.sleep(10)  # 出错时等待10秒
    
    def _run_prediction_cycle(self):
        """定时运行性能预测"""
        try:
            # 构建网络图
            network_graph = self._build_network_graph()
            
            if not network_graph:
                return
            
            # 运行预测
            predicted_kpis = self.predictor.predict(network_graph)
            
            # 更新统计
            self.stats['prediction_count'] += 1
            
            # 发送预测更新信号
            self.prediction_updated.emit(predicted_kpis)
            
            # 将预测结果反馈给编排器
            self.orchestrator.update_with_prediction(predicted_kpis)
            
            # 保存预测结果
            if self.database:
                prediction_data = {
                    'node_id': 'global',
                    'predicted_latency': float(predicted_kpis.get('latency', '0').replace('ms', '')),
                    'predicted_packet_loss': float(predicted_kpis.get('packet_loss', '0').replace('%', '')) / 100,
                    'predicted_throughput': float(predicted_kpis.get('throughput', '0').replace('Mbps', ''))
                }
                self.database.save_prediction_result(prediction_data)
            
            # 检查告警条件
            self._check_prediction_alerts(predicted_kpis)
            
        except Exception as e:
            self.logger.error(f"预测周期运行失败: {e}")
    
    def _build_network_graph(self) -> Dict[str, Any]:
        """构建网络图用于GNN预测"""
        try:
            # 获取网络状态
            network_state = self._get_current_network_state()
            
            if not network_state:
                return {}
            
            # 转换为GNN所需的图格式
            nodes = []
            for node_data in network_state.get('nodes', []):
                nodes.append({
                    'id': node_data['node_id'],
                    'type': 'switch',
                    'cpu_utilization': node_data['cpu_usage'] / node_data['cpu_capacity'],
                    'memory_utilization': node_data['memory_usage'] / node_data['memory_capacity'],
                    'packet_count': 1000,  # 模拟值
                    'byte_count': 1000000,  # 模拟值
                    'features': []
                })
            
            edges = []
            for link_data in network_state.get('links', []):
                edges.append({
                    'src': link_data['src_node'],
                    'dst': link_data['dst_node'],
                    'bandwidth_utilization': link_data['bandwidth_usage'] / link_data['bandwidth_capacity'],
                    'latency': link_data['latency'],
                    'packet_loss': 0.001,  # 模拟值
                    'features': []
                })
            
            return {
                'nodes': nodes,
                'edges': edges,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"网络图构建失败: {e}")
            return {}
    
    def _check_prediction_alerts(self, predicted_kpis: Dict[str, Any]):
        """检查预测告警条件"""
        try:
            # 解析预测值
            latency = float(predicted_kpis.get('latency', '0').replace('ms', ''))
            packet_loss = float(predicted_kpis.get('packet_loss', '0').replace('%', ''))
            congestion_level = predicted_kpis.get('congestion_level', 0.0)
            
            # 检查告警条件
            if latency > 100:
                self.system_alert.emit("WARNING", f"预测延迟过高: {latency:.2f}ms")
            
            if packet_loss > 1.0:
                self.system_alert.emit("WARNING", f"预测丢包率过高: {packet_loss:.2f}%")
            
            if congestion_level > 0.8:
                self.system_alert.emit("CRITICAL", f"预测网络拥塞严重: {congestion_level:.2f}")
            
        except Exception as e:
            self.logger.error(f"告警检查失败: {e}")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            current_time = time.time()
            uptime = current_time - self.stats['start_time'] if self.stats['start_time'] else 0
            
            stats = {
                'is_running': self.is_running,
                'uptime': uptime,
                'total_flows': self.stats['total_flows'],
                'industrial_flows': self.stats['industrial_flows'],
                'orchestrated_flows': self.stats['orchestrated_flows'],
                'prediction_count': self.stats['prediction_count'],
                'flow_queue_size': self.flow_queue.qsize(),
                'industrial_flow_ratio': (self.stats['industrial_flows'] / max(1, self.stats['total_flows'])) * 100,
                'orchestration_success_rate': (self.stats['orchestrated_flows'] / max(1, self.stats['industrial_flows'])) * 100
            }
            
            # 添加模块状态
            if self.network_interface:
                stats.update(self.network_interface.get_interface_stats())
            
            if self.classifier:
                stats.update(self.classifier.get_classification_stats())
            
            if self.orchestrator:
                stats.update(self.orchestrator.get_orchestration_stats())
            
            if self.predictor:
                stats.update(self.predictor.get_prediction_stats())
            
            return stats
            
        except Exception as e:
            self.logger.error(f"统计信息获取失败: {e}")
            return {'error': str(e)}
    
    def update_configuration(self, config_updates: Dict[str, Any]) -> bool:
        """更新系统配置"""
        try:
            for section, updates in config_updates.items():
                for key, value in updates.items():
                    self.config_manager.update_config(section, key, value)
            
            self.logger.info("系统配置更新成功")
            return True
            
        except Exception as e:
            self.logger.error(f"配置更新失败: {e}")
            return False
