"""
基于粒子群优化的资源动态编排器
实现PSO算法，用于工业互联网中时间确定性流量的资源预留和路径规划
"""

import numpy as np
import logging
import random
import copy
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
import time


@dataclass
class NetworkNode:
    """网络节点"""
    node_id: str
    cpu_capacity: float
    memory_capacity: float
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    
    def get_cpu_utilization(self) -> float:
        return self.cpu_usage / self.cpu_capacity if self.cpu_capacity > 0 else 0.0
    
    def get_memory_utilization(self) -> float:
        return self.memory_usage / self.memory_capacity if self.memory_capacity > 0 else 0.0


@dataclass
class NetworkLink:
    """网络链路"""
    link_id: str
    src_node: str
    dst_node: str
    bandwidth_capacity: float
    latency: float
    bandwidth_usage: float = 0.0
    
    def get_bandwidth_utilization(self) -> float:
        return self.bandwidth_usage / self.bandwidth_capacity if self.bandwidth_capacity > 0 else 0.0
    
    def get_available_bandwidth(self) -> float:
        return max(0, self.bandwidth_capacity - self.bandwidth_usage)


@dataclass
class FlowRequirement:
    """流量需求"""
    flow_id: str
    src_node: str
    dst_node: str
    bandwidth_requirement: float
    latency_requirement: float
    packet_loss_requirement: float
    priority: int = 1  # 1-5, 5为最高优先级


@dataclass
class Particle:
    """PSO粒子"""
    position: List[int]  # 路径表示
    velocity: List[float]
    fitness: float
    best_position: List[int]
    best_fitness: float


class PSOOrchestrator:
    """基于粒子群优化的资源动态编排器"""
    
    def __init__(self, pso_params: Dict[str, Any] = None):
        """
        初始化PSO编排器
        :param pso_params: PSO算法参数
        """
        self.logger = logging.getLogger(__name__)
        
        # PSO参数
        default_params = {
            'population_size': 50,
            'max_iterations': 100,
            'inertia_weight': 0.9,
            'cognitive_coefficient': 2.0,
            'social_coefficient': 2.0,
            'convergence_threshold': 1e-6
        }
        self.params = {**default_params, **(pso_params or {})}
        
        # 网络状态
        self.nodes: Dict[str, NetworkNode] = {}
        self.links: Dict[str, NetworkLink] = {}
        self.topology: Dict[str, List[str]] = {}  # 邻接表
        
        # 编排历史
        self.orchestration_history: List[Dict[str, Any]] = []
        
        # 性能预测数据
        self.predicted_performance: Dict[str, Any] = {}
        
        self.logger.info("PSO资源编排器初始化完成")
    
    def update_network_state(self, nodes: List[NetworkNode], links: List[NetworkLink]):
        """更新网络状态"""
        try:
            # 更新节点信息
            self.nodes = {node.node_id: node for node in nodes}
            
            # 更新链路信息
            self.links = {link.link_id: link for link in links}
            
            # 构建拓扑图
            self.topology = {}
            for link in links:
                if link.src_node not in self.topology:
                    self.topology[link.src_node] = []
                if link.dst_node not in self.topology:
                    self.topology[link.dst_node] = []
                
                self.topology[link.src_node].append(link.dst_node)
                # 假设链路是双向的
                self.topology[link.dst_node].append(link.src_node)
            
            self.logger.info(f"网络状态更新完成: {len(nodes)}个节点, {len(links)}条链路")
            
        except Exception as e:
            self.logger.error(f"网络状态更新失败: {e}")
    
    def run_pso_optimization(self, flow_requirement: FlowRequirement, 
                           network_state: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        为指定的时间确定性流量运行PSO算法，寻找最优资源分配方案
        :param flow_requirement: 流量需求
        :param network_state: 当前网络状态
        :return: 最优编排计划，失败则返回None
        """
        try:
            self.logger.info(f"开始PSO优化，流量ID: {flow_requirement.flow_id}")
            
            # 检查网络连通性
            if not self._check_connectivity(flow_requirement.src_node, flow_requirement.dst_node):
                self.logger.warning(f"源节点 {flow_requirement.src_node} 到目标节点 {flow_requirement.dst_node} 不连通")
                return None
            
            # 初始化粒子群
            particles = self._initialize_particles(flow_requirement)
            if not particles:
                self.logger.error("粒子群初始化失败")
                return None
            
            # PSO主循环
            global_best_particle = min(particles, key=lambda p: p.best_fitness)
            convergence_count = 0
            
            for iteration in range(self.params['max_iterations']):
                # 更新粒子
                for particle in particles:
                    self._update_particle(particle, global_best_particle, flow_requirement)
                
                # 更新全局最优
                current_best = min(particles, key=lambda p: p.best_fitness)
                if current_best.best_fitness < global_best_particle.best_fitness:
                    improvement = global_best_particle.best_fitness - current_best.best_fitness
                    global_best_particle = current_best
                    convergence_count = 0
                    self.logger.debug(f"迭代 {iteration}: 找到更优解，适应度: {current_best.best_fitness:.6f}")
                else:
                    convergence_count += 1
                
                # 检查收敛
                if convergence_count >= 10 or current_best.best_fitness < self.params['convergence_threshold']:
                    self.logger.info(f"PSO在第 {iteration} 次迭代收敛")
                    break
            
            # 生成编排计划
            if global_best_particle.best_fitness < float('inf'):
                plan = self._generate_orchestration_plan(global_best_particle, flow_requirement)
                self.logger.info(f"PSO优化完成，最优适应度: {global_best_particle.best_fitness:.6f}")
                return plan
            else:
                self.logger.warning("PSO优化未找到可行解")
                return None
                
        except Exception as e:
            self.logger.error(f"PSO优化失败: {e}")
            return None
    
    def _check_connectivity(self, src: str, dst: str) -> bool:
        """检查两个节点之间的连通性"""
        if src not in self.topology or dst not in self.topology:
            return False
        
        visited = set()
        queue = [src]
        
        while queue:
            current = queue.pop(0)
            if current == dst:
                return True
            
            if current in visited:
                continue
            
            visited.add(current)
            queue.extend(neighbor for neighbor in self.topology.get(current, []) 
                        if neighbor not in visited)
        
        return False
    
    def _initialize_particles(self, flow_requirement: FlowRequirement) -> List[Particle]:
        """初始化粒子群"""
        try:
            particles = []
            
            # 找到所有可能的路径
            all_paths = self._find_all_paths(flow_requirement.src_node, flow_requirement.dst_node)
            if not all_paths:
                return []
            
            # 限制路径数量以提高效率
            max_paths = min(len(all_paths), 20)
            selected_paths = random.sample(all_paths, max_paths) if len(all_paths) > max_paths else all_paths
            
            for i in range(self.params['population_size']):
                # 随机选择一条路径作为初始位置
                path = random.choice(selected_paths)
                position = self._path_to_position(path)
                
                # 初始化粒子
                particle = Particle(
                    position=position,
                    velocity=[0.0] * len(position),
                    fitness=float('inf'),
                    best_position=position.copy(),
                    best_fitness=float('inf')
                )
                
                # 计算初始适应度
                particle.fitness = self._calculate_fitness(particle.position, flow_requirement)
                particle.best_fitness = particle.fitness
                
                particles.append(particle)
            
            self.logger.info(f"初始化 {len(particles)} 个粒子")
            return particles
            
        except Exception as e:
            self.logger.error(f"粒子群初始化失败: {e}")
            return []
    
    def _find_all_paths(self, src: str, dst: str, max_length: int = 6) -> List[List[str]]:
        """找到两个节点之间的所有路径"""
        def dfs(current, target, path, visited, all_paths):
            if len(path) > max_length:
                return
            
            if current == target:
                all_paths.append(path.copy())
                return
            
            for neighbor in self.topology.get(current, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    path.append(neighbor)
                    dfs(neighbor, target, path, visited, all_paths)
                    path.pop()
                    visited.remove(neighbor)
        
        all_paths = []
        visited = {src}
        dfs(src, dst, [src], visited, all_paths)
        return all_paths
    
    def _path_to_position(self, path: List[str]) -> List[int]:
        """将路径转换为粒子位置编码"""
        # 简单编码：使用节点索引
        node_list = list(self.nodes.keys())
        position = []
        
        for node in path:
            if node in node_list:
                position.append(node_list.index(node))
        
        return position
    
    def _position_to_path(self, position: List[int]) -> List[str]:
        """将粒子位置解码为路径"""
        node_list = list(self.nodes.keys())
        path = []
        
        for idx in position:
            if 0 <= idx < len(node_list):
                path.append(node_list[idx])
        
        return path
    
    def _calculate_fitness(self, position: List[int], flow_requirement: FlowRequirement) -> float:
        """
        计算粒子适应度
        适应度函数综合考虑时延、资源占用、路径长度等因素
        """
        try:
            path = self._position_to_path(position)
            
            if len(path) < 2:
                return float('inf')
            
            # 检查路径有效性
            if not self._is_valid_path(path):
                return float('inf')
            
            # 计算总时延
            total_latency = 0.0
            total_bandwidth_cost = 0.0
            path_length = len(path) - 1
            
            for i in range(len(path) - 1):
                src_node = path[i]
                dst_node = path[i + 1]
                
                # 找到对应的链路
                link = self._find_link(src_node, dst_node)
                if not link:
                    return float('inf')
                
                # 检查带宽是否足够
                if link.get_available_bandwidth() < flow_requirement.bandwidth_requirement:
                    return float('inf')
                
                total_latency += link.latency
                total_bandwidth_cost += flow_requirement.bandwidth_requirement / link.bandwidth_capacity
            
            # 检查时延约束
            if total_latency > flow_requirement.latency_requirement:
                return float('inf')
            
            # 计算适应度（越小越好）
            fitness = (
                total_latency * 1.0 +  # 时延权重
                total_bandwidth_cost * 0.5 +  # 带宽占用权重
                path_length * 0.1  # 路径长度权重
            )
            
            # 考虑优先级
            fitness /= flow_requirement.priority
            
            return fitness
            
        except Exception as e:
            self.logger.error(f"适应度计算失败: {e}")
            return float('inf')
    
    def _is_valid_path(self, path: List[str]) -> bool:
        """检查路径是否有效"""
        if len(path) < 2:
            return False
        
        for i in range(len(path) - 1):
            if path[i + 1] not in self.topology.get(path[i], []):
                return False
        
        return True
    
    def _find_link(self, src: str, dst: str) -> Optional[NetworkLink]:
        """查找两个节点之间的链路"""
        for link in self.links.values():
            if (link.src_node == src and link.dst_node == dst) or \
               (link.src_node == dst and link.dst_node == src):
                return link
        return None
    
    def _update_particle(self, particle: Particle, global_best: Particle, flow_requirement: FlowRequirement):
        """更新粒子的速度和位置"""
        try:
            w = self.params['inertia_weight']
            c1 = self.params['cognitive_coefficient']
            c2 = self.params['social_coefficient']
            
            # 更新速度
            for i in range(len(particle.velocity)):
                r1, r2 = random.random(), random.random()
                
                cognitive_component = c1 * r1 * (particle.best_position[i] - particle.position[i])
                social_component = c2 * r2 * (global_best.best_position[i] - particle.position[i])
                
                particle.velocity[i] = (w * particle.velocity[i] + 
                                      cognitive_component + social_component)
                
                # 限制速度
                particle.velocity[i] = max(-3, min(3, particle.velocity[i]))
            
            # 更新位置
            for i in range(len(particle.position)):
                particle.position[i] = int(particle.position[i] + particle.velocity[i])
                particle.position[i] = max(0, min(len(self.nodes) - 1, particle.position[i]))
            
            # 计算新的适应度
            new_fitness = self._calculate_fitness(particle.position, flow_requirement)
            particle.fitness = new_fitness
            
            # 更新个体最优
            if new_fitness < particle.best_fitness:
                particle.best_fitness = new_fitness
                particle.best_position = particle.position.copy()
                
        except Exception as e:
            self.logger.error(f"粒子更新失败: {e}")
    
    def _generate_orchestration_plan(self, best_particle: Particle, 
                                   flow_requirement: FlowRequirement) -> Dict[str, Any]:
        """生成编排计划"""
        try:
            path = self._position_to_path(best_particle.best_position)
            
            # 计算路径详细信息
            path_info = []
            total_latency = 0.0
            
            for i in range(len(path) - 1):
                src_node = path[i]
                dst_node = path[i + 1]
                link = self._find_link(src_node, dst_node)
                
                if link:
                    path_info.append({
                        'src': src_node,
                        'dst': dst_node,
                        'link_id': link.link_id,
                        'latency': link.latency,
                        'bandwidth_reserved': flow_requirement.bandwidth_requirement
                    })
                    total_latency += link.latency
            
            plan = {
                'type': 'path_reservation',
                'flow_id': flow_requirement.flow_id,
                'path': path,
                'path_info': path_info,
                'total_latency': total_latency,
                'bandwidth_requirement': flow_requirement.bandwidth_requirement,
                'fitness_score': best_particle.best_fitness,
                'timestamp': time.time(),
                'qos_requirements': {
                    'latency': flow_requirement.latency_requirement,
                    'bandwidth': flow_requirement.bandwidth_requirement,
                    'packet_loss': flow_requirement.packet_loss_requirement
                }
            }
            
            # 记录编排历史
            self.orchestration_history.append(plan)
            
            return plan
            
        except Exception as e:
            self.logger.error(f"编排计划生成失败: {e}")
            return None
    
    def update_with_prediction(self, predicted_kpis: Dict[str, Any]):
        """根据性能预测结果，调整编排策略"""
        try:
            self.predicted_performance = predicted_kpis
            
            # 根据预测结果调整PSO参数
            predicted_congestion = predicted_kpis.get('congestion_level', 0.0)
            
            if predicted_congestion > 0.8:
                # 高拥塞情况下，增加搜索强度
                self.params['population_size'] = min(80, self.params['population_size'] * 1.2)
                self.params['max_iterations'] = min(150, self.params['max_iterations'] * 1.2)
            elif predicted_congestion < 0.3:
                # 低拥塞情况下，可以减少搜索强度
                self.params['population_size'] = max(30, self.params['population_size'] * 0.8)
                self.params['max_iterations'] = max(50, self.params['max_iterations'] * 0.8)
            
            self.logger.info(f"根据预测结果调整PSO参数: 拥塞级别 {predicted_congestion:.2f}")
            
        except Exception as e:
            self.logger.error(f"预测结果更新失败: {e}")
    
    def get_orchestration_stats(self) -> Dict[str, Any]:
        """获取编排统计信息"""
        try:
            if not self.orchestration_history:
                return {'total_orchestrations': 0}
            
            total_orchestrations = len(self.orchestration_history)
            avg_fitness = np.mean([plan['fitness_score'] for plan in self.orchestration_history])
            avg_latency = np.mean([plan['total_latency'] for plan in self.orchestration_history])
            
            return {
                'total_orchestrations': total_orchestrations,
                'average_fitness': avg_fitness,
                'average_latency': avg_latency,
                'network_nodes': len(self.nodes),
                'network_links': len(self.links),
                'pso_parameters': self.params
            }
            
        except Exception as e:
            self.logger.error(f"统计信息获取失败: {e}")
            return {'error': str(e)}
