"""
基于图神经网络的网络性能预测器
实现GNN算法，用于预测网络未来的时延、丢包率等关键性能指标
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import json
import os
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
import time
from collections import deque


@dataclass
class NetworkGraphNode:
    """网络图节点"""
    node_id: str
    node_type: str  # 'switch', 'router', 'host'
    cpu_utilization: float
    memory_utilization: float
    packet_count: int
    byte_count: int
    features: List[float]


@dataclass
class NetworkGraphEdge:
    """网络图边"""
    src_node: str
    dst_node: str
    bandwidth_utilization: float
    latency: float
    packet_loss: float
    features: List[float]


class GraphConvLayer(nn.Module):
    """图卷积层"""
    
    def __init__(self, input_dim: int, output_dim: int):
        super(GraphConvLayer, self).__init__()
        self.linear = nn.Linear(input_dim, output_dim)
        self.activation = nn.ReLU()
        
    def forward(self, node_features, adjacency_matrix):
        """
        前向传播
        :param node_features: 节点特征矩阵 [num_nodes, input_dim]
        :param adjacency_matrix: 邻接矩阵 [num_nodes, num_nodes]
        """
        # 图卷积操作: A * X * W
        aggregated = torch.matmul(adjacency_matrix, node_features)
        output = self.linear(aggregated)
        return self.activation(output)


class GNNModel(nn.Module):
    """图神经网络模型"""
    
    def __init__(self, node_feature_dim: int, edge_feature_dim: int, 
                 hidden_dim: int = 64, output_dim: int = 3):
        """
        初始化GNN模型
        :param node_feature_dim: 节点特征维度
        :param edge_feature_dim: 边特征维度
        :param hidden_dim: 隐藏层维度
        :param output_dim: 输出维度 (latency, packet_loss, throughput)
        """
        super(GNNModel, self).__init__()
        
        self.node_feature_dim = node_feature_dim
        self.edge_feature_dim = edge_feature_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        
        # 图卷积层
        self.conv1 = GraphConvLayer(node_feature_dim, hidden_dim)
        self.conv2 = GraphConvLayer(hidden_dim, hidden_dim)
        self.conv3 = GraphConvLayer(hidden_dim, hidden_dim)
        
        # 边特征处理
        self.edge_encoder = nn.Sequential(
            nn.Linear(edge_feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
    def forward(self, node_features, edge_features, adjacency_matrix):
        """
        前向传播
        :param node_features: 节点特征 [num_nodes, node_feature_dim]
        :param edge_features: 边特征 [num_edges, edge_feature_dim]
        :param adjacency_matrix: 邻接矩阵 [num_nodes, num_nodes]
        """
        # 节点特征处理
        x = self.conv1(node_features, adjacency_matrix)
        x = self.conv2(x, adjacency_matrix)
        x = self.conv3(x, adjacency_matrix)
        
        # 边特征处理
        edge_emb = self.edge_encoder(edge_features)
        edge_global = torch.mean(edge_emb, dim=0, keepdim=True)
        
        # 节点特征全局池化
        node_global = torch.mean(x, dim=0, keepdim=True)
        
        # 特征融合
        combined = torch.cat([node_global, edge_global], dim=1)
        
        # 输出预测
        output = self.output_layer(combined)
        
        return output


class GNNPredictor:
    """基于GNN的网络性能预测器"""
    
    def __init__(self, model_path: str = None, device: str = 'cpu'):
        """
        初始化GNN预测器
        :param model_path: 预训练模型路径
        :param device: 计算设备 ('cpu' 或 'cuda')
        """
        self.model_path = model_path
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.logger = logging.getLogger(__name__)
        
        # 历史数据缓存
        self.history_window = 100
        self.node_history = deque(maxlen=self.history_window)
        self.edge_history = deque(maxlen=self.history_window)
        self.prediction_history = deque(maxlen=self.history_window)
        
        # 特征标准化参数
        self.node_feature_stats = {'mean': None, 'std': None}
        self.edge_feature_stats = {'mean': None, 'std': None}
        
        # 预测精度统计
        self.accuracy_stats = {
            'latency_errors': deque(maxlen=100),
            'packet_loss_errors': deque(maxlen=100),
            'throughput_errors': deque(maxlen=100)
        }
        
        # 加载模型
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            self._init_default_model()
        
        self.logger.info(f"GNN预测器初始化完成，设备: {self.device}")
    
    def _init_default_model(self):
        """初始化默认模型"""
        try:
            # 默认特征维度
            node_feature_dim = 6  # cpu, memory, packet_count, byte_count, etc.
            edge_feature_dim = 4  # bandwidth_util, latency, packet_loss, etc.
            
            self.model = GNNModel(
                node_feature_dim=node_feature_dim,
                edge_feature_dim=edge_feature_dim,
                hidden_dim=64,
                output_dim=3
            ).to(self.device)
            
            self.logger.info("初始化默认GNN模型")
            
        except Exception as e:
            self.logger.error(f"默认模型初始化失败: {e}")
    
    def predict(self, network_graph: Dict[str, Any]) -> Dict[str, Any]:
        """
        输入当前网络状态的图表示，预测未来性能
        :param network_graph: 包含节点和链路特征的网络图数据
        :return: 预测的KPI字典
        """
        try:
            if not self.model:
                self.logger.error("模型未初始化")
                return self._get_default_prediction()
            
            # 解析网络图数据
            nodes, edges, adjacency_matrix = self._parse_network_graph(network_graph)
            
            if not nodes or not edges:
                self.logger.warning("网络图数据为空")
                return self._get_default_prediction()
            
            # 准备输入数据
            node_features = self._prepare_node_features(nodes)
            edge_features = self._prepare_edge_features(edges)
            adj_matrix = self._prepare_adjacency_matrix(adjacency_matrix, len(nodes))
            
            # 转换为张量
            node_tensor = torch.FloatTensor(node_features).to(self.device)
            edge_tensor = torch.FloatTensor(edge_features).to(self.device)
            adj_tensor = torch.FloatTensor(adj_matrix).to(self.device)
            
            # 模型预测
            self.model.eval()
            with torch.no_grad():
                predictions = self.model(node_tensor, edge_tensor, adj_tensor)
                predictions = predictions.cpu().numpy().flatten()
            
            # 解析预测结果
            predicted_latency = max(0.1, predictions[0] * 100)  # 转换为毫秒
            predicted_packet_loss = max(0.0, min(1.0, predictions[1]))  # 限制在[0,1]
            predicted_throughput = max(1.0, predictions[2] * 1000)  # 转换为Mbps
            
            # 计算置信度
            confidence = self._calculate_confidence(predictions)
            
            # 构建预测结果
            kpis = {
                'latency': f"{predicted_latency:.2f}ms",
                'packet_loss': f"{predicted_packet_loss * 100:.3f}%",
                'throughput': f"{predicted_throughput:.2f}Mbps",
                'confidence': f"{confidence:.2f}",
                'prediction_timestamp': time.time(),
                'congestion_level': min(1.0, predicted_latency / 100 + predicted_packet_loss)
            }
            
            # 记录预测历史
            self.prediction_history.append({
                'timestamp': time.time(),
                'predictions': predictions.tolist(),
                'kpis': kpis
            })
            
            self.logger.debug(f"GNN预测完成: {kpis}")
            return kpis
            
        except Exception as e:
            self.logger.error(f"GNN预测失败: {e}")
            return self._get_default_prediction()
    
    def _parse_network_graph(self, network_graph: Dict[str, Any]) -> Tuple[List, List, Dict]:
        """解析网络图数据"""
        try:
            nodes = []
            edges = []
            adjacency = {}
            
            # 解析节点
            for node_data in network_graph.get('nodes', []):
                node = NetworkGraphNode(
                    node_id=node_data.get('id', ''),
                    node_type=node_data.get('type', 'switch'),
                    cpu_utilization=node_data.get('cpu_utilization', 0.0),
                    memory_utilization=node_data.get('memory_utilization', 0.0),
                    packet_count=node_data.get('packet_count', 0),
                    byte_count=node_data.get('byte_count', 0),
                    features=node_data.get('features', [])
                )
                nodes.append(node)
            
            # 解析边
            for edge_data in network_graph.get('edges', []):
                edge = NetworkGraphEdge(
                    src_node=edge_data.get('src', ''),
                    dst_node=edge_data.get('dst', ''),
                    bandwidth_utilization=edge_data.get('bandwidth_utilization', 0.0),
                    latency=edge_data.get('latency', 0.0),
                    packet_loss=edge_data.get('packet_loss', 0.0),
                    features=edge_data.get('features', [])
                )
                edges.append(edge)
                
                # 构建邻接关系
                if edge.src_node not in adjacency:
                    adjacency[edge.src_node] = []
                adjacency[edge.src_node].append(edge.dst_node)
            
            return nodes, edges, adjacency
            
        except Exception as e:
            self.logger.error(f"网络图解析失败: {e}")
            return [], [], {}
    
    def _prepare_node_features(self, nodes: List[NetworkGraphNode]) -> np.ndarray:
        """准备节点特征矩阵"""
        try:
            features = []
            
            for node in nodes:
                node_features = [
                    node.cpu_utilization,
                    node.memory_utilization,
                    min(node.packet_count / 10000, 1.0),  # 归一化
                    min(node.byte_count / 1000000, 1.0),  # 归一化
                    1.0 if node.node_type == 'switch' else 0.0,
                    1.0 if node.node_type == 'router' else 0.0
                ]
                
                # 添加自定义特征
                if node.features:
                    node_features.extend(node.features[:4])  # 最多添加4个自定义特征
                
                # 确保特征长度一致
                while len(node_features) < 6:
                    node_features.append(0.0)
                
                features.append(node_features[:6])  # 限制为6维
            
            features_array = np.array(features, dtype=np.float32)
            
            # 特征标准化
            if self.node_feature_stats['mean'] is not None:
                features_array = (features_array - self.node_feature_stats['mean']) / \
                                (self.node_feature_stats['std'] + 1e-8)
            
            return features_array
            
        except Exception as e:
            self.logger.error(f"节点特征准备失败: {e}")
            return np.zeros((len(nodes), 6), dtype=np.float32)
    
    def _prepare_edge_features(self, edges: List[NetworkGraphEdge]) -> np.ndarray:
        """准备边特征矩阵"""
        try:
            features = []
            
            for edge in edges:
                edge_features = [
                    edge.bandwidth_utilization,
                    min(edge.latency / 100, 1.0),  # 归一化延迟
                    edge.packet_loss,
                    1.0  # 边存在标志
                ]
                
                # 添加自定义特征
                if edge.features:
                    edge_features.extend(edge.features[:2])
                
                # 确保特征长度一致
                while len(edge_features) < 4:
                    edge_features.append(0.0)
                
                features.append(edge_features[:4])
            
            features_array = np.array(features, dtype=np.float32)
            
            # 特征标准化
            if self.edge_feature_stats['mean'] is not None:
                features_array = (features_array - self.edge_feature_stats['mean']) / \
                                (self.edge_feature_stats['std'] + 1e-8)
            
            return features_array
            
        except Exception as e:
            self.logger.error(f"边特征准备失败: {e}")
            return np.zeros((len(edges), 4), dtype=np.float32)
    
    def _prepare_adjacency_matrix(self, adjacency: Dict[str, List[str]], num_nodes: int) -> np.ndarray:
        """准备邻接矩阵"""
        try:
            # 创建节点ID到索引的映射
            node_ids = list(adjacency.keys())
            node_to_idx = {node_id: idx for idx, node_id in enumerate(node_ids)}
            
            # 初始化邻接矩阵
            adj_matrix = np.eye(num_nodes, dtype=np.float32)  # 添加自环
            
            # 填充邻接关系
            for src_node, neighbors in adjacency.items():
                if src_node in node_to_idx:
                    src_idx = node_to_idx[src_node]
                    for dst_node in neighbors:
                        if dst_node in node_to_idx:
                            dst_idx = node_to_idx[dst_node]
                            adj_matrix[src_idx][dst_idx] = 1.0
            
            # 度归一化
            degree = np.sum(adj_matrix, axis=1, keepdims=True)
            degree[degree == 0] = 1  # 避免除零
            adj_matrix = adj_matrix / degree
            
            return adj_matrix
            
        except Exception as e:
            self.logger.error(f"邻接矩阵准备失败: {e}")
            return np.eye(num_nodes, dtype=np.float32)
    
    def _calculate_confidence(self, predictions: np.ndarray) -> float:
        """计算预测置信度"""
        try:
            # 基于历史预测精度计算置信度
            if not self.accuracy_stats['latency_errors']:
                return 0.8  # 默认置信度
            
            # 计算平均误差
            avg_latency_error = np.mean(list(self.accuracy_stats['latency_errors']))
            avg_packet_loss_error = np.mean(list(self.accuracy_stats['packet_loss_errors']))
            avg_throughput_error = np.mean(list(self.accuracy_stats['throughput_errors']))
            
            # 综合误差
            overall_error = (avg_latency_error + avg_packet_loss_error + avg_throughput_error) / 3
            
            # 转换为置信度 (误差越小，置信度越高)
            confidence = max(0.1, min(1.0, 1.0 - overall_error))
            
            return confidence
            
        except Exception as e:
            self.logger.error(f"置信度计算失败: {e}")
            return 0.5
    
    def _get_default_prediction(self) -> Dict[str, Any]:
        """获取默认预测结果"""
        return {
            'latency': '10.00ms',
            'packet_loss': '0.100%',
            'throughput': '100.00Mbps',
            'confidence': '0.50',
            'prediction_timestamp': time.time(),
            'congestion_level': 0.1
        }
    
    def update_accuracy(self, actual_kpis: Dict[str, Any]):
        """更新预测精度统计"""
        try:
            if not self.prediction_history:
                return
            
            # 获取最近的预测
            latest_prediction = self.prediction_history[-1]
            pred_time = latest_prediction['timestamp']
            
            # 检查时间匹配（允许一定误差）
            current_time = time.time()
            if abs(current_time - pred_time) > 300:  # 5分钟内的预测
                return
            
            # 计算误差
            predicted = latest_prediction['predictions']
            actual_latency = float(actual_kpis.get('latency', '0').replace('ms', '')) / 100
            actual_packet_loss = float(actual_kpis.get('packet_loss', '0').replace('%', '')) / 100
            actual_throughput = float(actual_kpis.get('throughput', '0').replace('Mbps', '')) / 1000
            
            latency_error = abs(predicted[0] - actual_latency)
            packet_loss_error = abs(predicted[1] - actual_packet_loss)
            throughput_error = abs(predicted[2] - actual_throughput)
            
            # 更新误差统计
            self.accuracy_stats['latency_errors'].append(latency_error)
            self.accuracy_stats['packet_loss_errors'].append(packet_loss_error)
            self.accuracy_stats['throughput_errors'].append(throughput_error)
            
            self.logger.debug(f"预测精度更新: 延迟误差 {latency_error:.4f}, "
                            f"丢包率误差 {packet_loss_error:.4f}, "
                            f"吞吐量误差 {throughput_error:.4f}")
            
        except Exception as e:
            self.logger.error(f"精度更新失败: {e}")
    
    def save_model(self, model_path: str) -> bool:
        """保存模型"""
        try:
            if self.model:
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'node_feature_stats': self.node_feature_stats,
                    'edge_feature_stats': self.edge_feature_stats
                }, model_path)
                self.logger.info(f"模型保存成功: {model_path}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, model_path: str) -> bool:
        """加载模型"""
        try:
            if not os.path.exists(model_path):
                self.logger.warning(f"模型文件不存在: {model_path}")
                return False
            
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 重新创建模型
            self._init_default_model()
            
            # 加载权重
            self.model.load_state_dict(checkpoint['model_state_dict'])
            
            # 加载统计信息
            if 'node_feature_stats' in checkpoint:
                self.node_feature_stats = checkpoint['node_feature_stats']
            if 'edge_feature_stats' in checkpoint:
                self.edge_feature_stats = checkpoint['edge_feature_stats']
            
            self.logger.info(f"模型加载成功: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
    
    def get_prediction_stats(self) -> Dict[str, Any]:
        """获取预测统计信息"""
        try:
            stats = {
                'total_predictions': len(self.prediction_history),
                'model_loaded': self.model is not None,
                'device': str(self.device),
                'history_window': self.history_window
            }
            
            if self.accuracy_stats['latency_errors']:
                stats.update({
                    'avg_latency_error': np.mean(list(self.accuracy_stats['latency_errors'])),
                    'avg_packet_loss_error': np.mean(list(self.accuracy_stats['packet_loss_errors'])),
                    'avg_throughput_error': np.mean(list(self.accuracy_stats['throughput_errors']))
                })
            
            return stats
            
        except Exception as e:
            self.logger.error(f"统计信息获取失败: {e}")
            return {'error': str(e)}
