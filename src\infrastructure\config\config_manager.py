"""
配置管理模块
负责系统配置的加载、保存和管理
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class DatabaseConfig:
    """数据库配置"""
    db_path: str = "data/system.db"
    backup_enabled: bool = True
    backup_interval: int = 3600  # 秒


@dataclass
class NetworkConfig:
    """网络配置"""
    default_interface: str = "auto"
    capture_filter: str = ""
    max_packet_size: int = 65535
    capture_timeout: int = 1000


@dataclass
class PLSAConfig:
    """PLSA分类器配置"""
    model_path: str = "models/plsa_model.pkl"
    feature_mapping_path: str = "models/feature_mapping.json"
    classification_threshold: float = 0.8
    retrain_interval: int = 86400  # 秒


@dataclass
class PSOConfig:
    """PSO算法配置"""
    population_size: int = 50
    max_iterations: int = 100
    inertia_weight: float = 0.9
    cognitive_coefficient: float = 2.0
    social_coefficient: float = 2.0
    convergence_threshold: float = 1e-6


@dataclass
class GNNConfig:
    """GNN预测器配置"""
    model_path: str = "models/gnn_model.pth"
    prediction_window: int = 300  # 秒
    update_interval: int = 60  # 秒
    accuracy_threshold: float = 0.95


@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "default"
    language: str = "zh_CN"
    auto_refresh_interval: int = 5  # 秒
    max_log_entries: int = 1000
    chart_update_interval: int = 2  # 秒


@dataclass
class SystemConfig:
    """系统总配置"""
    debug_mode: bool = False
    log_level: str = "INFO"
    log_file: str = "logs/system.log"
    max_log_size: int = 10485760  # 10MB
    backup_count: int = 5
    
    database: DatabaseConfig = None
    network: NetworkConfig = None
    plsa: PLSAConfig = None
    pso: PSOConfig = None
    gnn: GNNConfig = None
    ui: UIConfig = None
    
    def __post_init__(self):
        if self.database is None:
            self.database = DatabaseConfig()
        if self.network is None:
            self.network = NetworkConfig()
        if self.plsa is None:
            self.plsa = PLSAConfig()
        if self.pso is None:
            self.pso = PSOConfig()
        if self.gnn is None:
            self.gnn = GNNConfig()
        if self.ui is None:
            self.ui = UIConfig()


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/system_config.json"):
        """
        初始化配置管理器
        :param config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = SystemConfig()
        self.logger = logging.getLogger(__name__)
        
        # 确保配置目录存在
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> bool:
        """从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 递归更新配置
                self._update_config_from_dict(self.config, config_data)
                
                self.logger.info(f"配置加载成功: {self.config_file}")
                return True
            else:
                # 配置文件不存在，创建默认配置
                self.save_config()
                self.logger.info(f"创建默认配置文件: {self.config_file}")
                return True
                
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            config_dict = asdict(self.config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"配置保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置保存失败: {e}")
            return False
    
    def _update_config_from_dict(self, config_obj, config_dict):
        """从字典递归更新配置对象"""
        for key, value in config_dict.items():
            if hasattr(config_obj, key):
                attr = getattr(config_obj, key)
                if hasattr(attr, '__dict__'):  # 嵌套配置对象
                    if isinstance(value, dict):
                        self._update_config_from_dict(attr, value)
                else:
                    setattr(config_obj, key, value)
    
    def get_config(self) -> SystemConfig:
        """获取系统配置"""
        return self.config
    
    def update_config(self, section: str, key: str, value: Any) -> bool:
        """
        更新配置项
        :param section: 配置段名称 (如 'database', 'network')
        :param key: 配置键名
        :param value: 配置值
        """
        try:
            if hasattr(self.config, section):
                section_obj = getattr(self.config, section)
                if hasattr(section_obj, key):
                    setattr(section_obj, key, value)
                    self.save_config()
                    self.logger.info(f"配置更新成功: {section}.{key} = {value}")
                    return True
                else:
                    self.logger.error(f"配置键不存在: {section}.{key}")
                    return False
            else:
                self.logger.error(f"配置段不存在: {section}")
                return False
                
        except Exception as e:
            self.logger.error(f"配置更新失败: {e}")
            return False
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        return self.config.database
    
    def get_network_config(self) -> NetworkConfig:
        """获取网络配置"""
        return self.config.network
    
    def get_plsa_config(self) -> PLSAConfig:
        """获取PLSA配置"""
        return self.config.plsa
    
    def get_pso_config(self) -> PSOConfig:
        """获取PSO配置"""
        return self.config.pso
    
    def get_gnn_config(self) -> GNNConfig:
        """获取GNN配置"""
        return self.config.gnn
    
    def get_ui_config(self) -> UIConfig:
        """获取UI配置"""
        return self.config.ui
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        try:
            self.config = SystemConfig()
            self.save_config()
            self.logger.info("配置已重置为默认值")
            return True
        except Exception as e:
            self.logger.error(f"配置重置失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证文件路径
            paths_to_check = [
                self.config.plsa.model_path,
                self.config.gnn.model_path,
                self.config.plsa.feature_mapping_path
            ]
            
            for path in paths_to_check:
                if not os.path.exists(os.path.dirname(path)):
                    os.makedirs(os.path.dirname(path), exist_ok=True)
            
            # 验证数值范围
            if not (0 < self.config.pso.inertia_weight <= 1):
                self.logger.warning("PSO惯性权重应在(0,1]范围内")
                return False
            
            if not (0 < self.config.plsa.classification_threshold <= 1):
                self.logger.warning("PLSA分类阈值应在(0,1]范围内")
                return False
            
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
