"""
数据库管理模块
负责所有数据持久化操作，包括用户信息、流量数据、策略配置等
"""

import sqlite3
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
import json
import os


class DatabaseManager:
    """数据库管理类，封装所有SQL操作"""
    
    def __init__(self, db_path: str = "data/system.db"):
        """
        初始化数据库连接
        :param db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.connection = None
        self.logger = logging.getLogger(__name__)
        
        # 确保数据目录存在
        db_dir = os.path.dirname(db_path)
        if db_dir:  # 只有当目录路径不为空时才创建
            os.makedirs(db_dir, exist_ok=True)
        
        self.connect()
        self.init_tables()
    
    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
            self.logger.info(f"数据库连接成功: {self.db_path}")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def init_tables(self):
        """初始化数据库表结构"""
        try:
            cursor = self.connection.cursor()
            
            # 用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    email VARCHAR(100),
                    role VARCHAR(20) DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # 流量数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS flow_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    src_ip VARCHAR(45) NOT NULL,
                    dst_ip VARCHAR(45) NOT NULL,
                    src_port INTEGER,
                    dst_port INTEGER,
                    protocol VARCHAR(10),
                    packet_size INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    classification VARCHAR(50),
                    fec_score REAL,
                    features TEXT,  -- JSON格式存储特征
                    is_deterministic BOOLEAN DEFAULT 0
                )
            ''')
            
            # 资源编排策略表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS orchestration_policies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    policy_name VARCHAR(100) NOT NULL,
                    target_flow_filter TEXT,  -- JSON格式的筛选条件
                    qos_requirements TEXT,    -- JSON格式的QoS要求
                    pso_parameters TEXT,      -- JSON格式的PSO参数
                    status VARCHAR(20) DEFAULT 'inactive',
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            # 网络性能预测结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prediction_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    node_id VARCHAR(50),
                    predicted_latency REAL,
                    predicted_packet_loss REAL,
                    predicted_throughput REAL,
                    prediction_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    actual_latency REAL,
                    actual_packet_loss REAL,
                    actual_throughput REAL,
                    accuracy_score REAL
                )
            ''')
            
            # 系统日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level VARCHAR(10),
                    module VARCHAR(50),
                    message TEXT,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            self.connection.commit()
            self.logger.info("数据库表初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库表初始化失败: {e}")
            raise
    
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """根据用户名获取用户信息"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            row = cursor.fetchone()
            return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
    
    def create_user(self, username: str, password_hash: str, email: str = None, role: str = 'user') -> bool:
        """创建新用户"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(
                "INSERT INTO users (username, password_hash, email, role) VALUES (?, ?, ?, ?)",
                (username, password_hash, email, role)
            )
            self.connection.commit()
            self.logger.info(f"用户创建成功: {username}")
            return True
        except Exception as e:
            self.logger.error(f"用户创建失败: {e}")
            return False
    
    def update_last_login(self, username: str) -> bool:
        """更新用户最后登录时间"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE username = ?",
                (username,)
            )
            self.connection.commit()
            return True
        except Exception as e:
            self.logger.error(f"更新登录时间失败: {e}")
            return False
    
    def save_flow_data(self, flow_data: Dict[str, Any]) -> bool:
        """保存流量数据"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO flow_data 
                (src_ip, dst_ip, src_port, dst_port, protocol, packet_size, 
                 classification, fec_score, features, is_deterministic)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                flow_data.get('src_ip'),
                flow_data.get('dst_ip'),
                flow_data.get('src_port'),
                flow_data.get('dst_port'),
                flow_data.get('protocol'),
                flow_data.get('packet_size'),
                flow_data.get('classification'),
                flow_data.get('fec_score'),
                json.dumps(flow_data.get('features', {})),
                flow_data.get('is_deterministic', False)
            ))
            self.connection.commit()
            return True
        except Exception as e:
            self.logger.error(f"保存流量数据失败: {e}")
            return False
    
    def save_policy(self, policy_data: Dict[str, Any]) -> bool:
        """保存编排策略"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO orchestration_policies 
                (policy_name, target_flow_filter, qos_requirements, pso_parameters, created_by)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                policy_data.get('policy_name'),
                json.dumps(policy_data.get('target_flow_filter', {})),
                json.dumps(policy_data.get('qos_requirements', {})),
                json.dumps(policy_data.get('pso_parameters', {})),
                policy_data.get('created_by')
            ))
            self.connection.commit()
            return True
        except Exception as e:
            self.logger.error(f"保存策略失败: {e}")
            return False
    
    def get_active_policies(self) -> List[Dict[str, Any]]:
        """获取所有激活的编排策略"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT * FROM orchestration_policies WHERE status = 'active'")
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"获取策略失败: {e}")
            return []
    
    def save_prediction_result(self, prediction_data: Dict[str, Any]) -> bool:
        """保存性能预测结果"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO prediction_results 
                (node_id, predicted_latency, predicted_packet_loss, predicted_throughput)
                VALUES (?, ?, ?, ?)
            ''', (
                prediction_data.get('node_id'),
                prediction_data.get('predicted_latency'),
                prediction_data.get('predicted_packet_loss'),
                prediction_data.get('predicted_throughput')
            ))
            self.connection.commit()
            return True
        except Exception as e:
            self.logger.error(f"保存预测结果失败: {e}")
            return False
    
    def log_system_event(self, level: str, module: str, message: str, details: str = None, user_id: int = None) -> bool:
        """记录系统日志"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO system_logs (level, module, message, details, user_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (level, module, message, details, user_id))
            self.connection.commit()
            return True
        except Exception as e:
            self.logger.error(f"记录系统日志失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.logger.info("数据库连接已关闭")
