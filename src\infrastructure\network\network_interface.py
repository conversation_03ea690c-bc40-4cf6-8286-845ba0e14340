"""
网络接口模块
负责网络流量捕获、分析和网络指令下发
"""

import logging
import threading
import time
from typing import Callable, Dict, Any, Optional, List
from scapy.all import sniff, get_if_list, Ether, IP, TCP, UDP
from scapy.layers.inet import ICMP
import json


class NetworkFlow:
    """网络流量对象"""
    
    def __init__(self, packet):
        """从数据包创建流量对象"""
        self.timestamp = time.time()
        self.src_ip = None
        self.dst_ip = None
        self.src_port = None
        self.dst_port = None
        self.protocol = None
        self.packet_size = len(packet)
        self.features = {}
        self.classification = None
        self.fec_score = None
        self.is_deterministic = False
        
        self._extract_packet_info(packet)
    
    def _extract_packet_info(self, packet):
        """从数据包中提取信息"""
        try:
            if IP in packet:
                self.src_ip = packet[IP].src
                self.dst_ip = packet[IP].dst
                self.protocol = packet[IP].proto
                
                if TCP in packet:
                    self.src_port = packet[TCP].sport
                    self.dst_port = packet[TCP].dport
                    self.protocol = "TCP"
                elif UDP in packet:
                    self.src_port = packet[UDP].sport
                    self.dst_port = packet[UDP].dport
                    self.protocol = "UDP"
                elif ICMP in packet:
                    self.protocol = "ICMP"
                
                # 提取流量特征用于分类
                self._extract_features(packet)
                
        except Exception as e:
            logging.error(f"数据包信息提取失败: {e}")
    
    def _extract_features(self, packet):
        """提取流量特征"""
        try:
            # 基础特征
            self.features = {
                'packet_size': self.packet_size,
                'protocol': self.protocol,
                'src_port': self.src_port,
                'dst_port': self.dst_port,
                'has_payload': len(packet.payload) > 0 if hasattr(packet, 'payload') else False
            }
            
            # TCP特征
            if TCP in packet:
                tcp_layer = packet[TCP]
                self.features.update({
                    'tcp_flags': tcp_layer.flags,
                    'tcp_window': tcp_layer.window,
                    'tcp_seq': tcp_layer.seq,
                    'tcp_ack': tcp_layer.ack
                })
            
            # 工业协议端口检测
            industrial_ports = {
                502: 'Modbus',
                44818: 'EtherNet/IP',
                2404: 'IEC 61850',
                20000: 'DNP3',
                47808: 'BACnet'
            }
            
            if self.dst_port in industrial_ports:
                self.features['industrial_protocol'] = industrial_ports[self.dst_port]
                self.is_deterministic = True
            
        except Exception as e:
            logging.error(f"特征提取失败: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp,
            'src_ip': self.src_ip,
            'dst_ip': self.dst_ip,
            'src_port': self.src_port,
            'dst_port': self.dst_port,
            'protocol': self.protocol,
            'packet_size': self.packet_size,
            'features': self.features,
            'classification': self.classification,
            'fec_score': self.fec_score,
            'is_deterministic': self.is_deterministic
        }


class NetworkInterface:
    """网络交互模块，负责抓包和下发指令"""
    
    def __init__(self, interface_name: str = None):
        """
        初始化网络接口
        :param interface_name: 网络接口名称，如果为None则自动选择
        """
        self.interface_name = interface_name or self._get_default_interface()
        self.is_capturing = False
        self.capture_thread = None
        self.packet_callback = None
        self.logger = logging.getLogger(__name__)
        
        # 流量统计
        self.packet_count = 0
        self.flow_count = 0
        self.start_time = None
        
        self.logger.info(f"网络接口初始化: {self.interface_name}")
    
    def _get_default_interface(self) -> str:
        """获取默认网络接口"""
        try:
            interfaces = get_if_list()
            # 过滤掉回环接口
            for iface in interfaces:
                if 'loopback' not in iface.lower() and 'lo' != iface.lower():
                    return iface
            return interfaces[0] if interfaces else "eth0"
        except Exception as e:
            self.logger.error(f"获取网络接口失败: {e}")
            return "eth0"
    
    def start_capture(self, callback_func: Callable[[NetworkFlow], None], filter_str: str = None):
        """
        开始网络流量捕获
        :param callback_func: 处理每个流量的回调函数
        :param filter_str: BPF过滤器字符串
        """
        if self.is_capturing:
            self.logger.warning("流量捕获已在运行中")
            return
        
        self.packet_callback = callback_func
        self.is_capturing = True
        self.start_time = time.time()
        self.packet_count = 0
        self.flow_count = 0
        
        # 在单独线程中运行捕获
        self.capture_thread = threading.Thread(
            target=self._capture_loop,
            args=(filter_str,),
            daemon=True
        )
        self.capture_thread.start()
        
        self.logger.info(f"开始在接口 {self.interface_name} 上捕获流量")
    
    def _capture_loop(self, filter_str: str = None):
        """流量捕获循环"""
        try:
            sniff(
                iface=self.interface_name,
                prn=self._process_packet,
                filter=filter_str,
                stop_filter=lambda x: not self.is_capturing,
                store=False  # 不存储数据包以节省内存
            )
        except Exception as e:
            self.logger.error(f"流量捕获异常: {e}")
            self.is_capturing = False
    
    def _process_packet(self, packet):
        """处理单个数据包"""
        try:
            self.packet_count += 1
            
            # 创建流量对象
            flow = NetworkFlow(packet)
            
            # 基础过滤：只处理IP流量
            if flow.src_ip and flow.dst_ip:
                self.flow_count += 1
                
                # 调用回调函数处理流量
                if self.packet_callback:
                    self.packet_callback(flow)
                
                # 定期输出统计信息
                if self.packet_count % 1000 == 0:
                    elapsed = time.time() - self.start_time
                    rate = self.packet_count / elapsed if elapsed > 0 else 0
                    self.logger.info(f"已处理 {self.packet_count} 个数据包，{self.flow_count} 个流量，速率: {rate:.2f} pps")
                    
        except Exception as e:
            self.logger.error(f"数据包处理失败: {e}")
    
    def stop_capture(self):
        """停止流量捕获"""
        if not self.is_capturing:
            return
        
        self.is_capturing = False
        
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=5)
        
        elapsed = time.time() - self.start_time if self.start_time else 0
        self.logger.info(f"流量捕获已停止，总计处理 {self.packet_count} 个数据包，耗时 {elapsed:.2f} 秒")
    
    def deploy_plan(self, plan: Dict[str, Any]) -> bool:
        """
        部署资源编排计划
        :param plan: 编排计划，包含路径、资源预留等信息
        :return: 部署是否成功
        """
        try:
            self.logger.info(f"部署编排计划: {plan}")
            
            # 这里应该实现具体的网络配置逻辑
            # 例如：配置OpenFlow流表、设置QoS策略等
            
            # 模拟部署过程
            plan_type = plan.get('type', 'unknown')
            
            if plan_type == 'path_reservation':
                return self._deploy_path_reservation(plan)
            elif plan_type == 'qos_configuration':
                return self._deploy_qos_configuration(plan)
            else:
                self.logger.warning(f"未知的编排计划类型: {plan_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"编排计划部署失败: {e}")
            return False
    
    def _deploy_path_reservation(self, plan: Dict[str, Any]) -> bool:
        """部署路径预留计划"""
        try:
            path = plan.get('path', [])
            bandwidth = plan.get('bandwidth', 0)
            
            self.logger.info(f"配置路径预留: {' -> '.join(path)}, 带宽: {bandwidth}")
            
            # 实际实现中，这里会调用SDN控制器API
            # 例如：OpenFlow流表配置、MPLS标签分配等
            
            return True
        except Exception as e:
            self.logger.error(f"路径预留部署失败: {e}")
            return False
    
    def _deploy_qos_configuration(self, plan: Dict[str, Any]) -> bool:
        """部署QoS配置"""
        try:
            qos_rules = plan.get('qos_rules', {})
            
            self.logger.info(f"配置QoS规则: {qos_rules}")
            
            # 实际实现中，这里会配置交换机的QoS策略
            # 例如：流量整形、优先级队列等
            
            return True
        except Exception as e:
            self.logger.error(f"QoS配置部署失败: {e}")
            return False
    
    def get_interface_stats(self) -> Dict[str, Any]:
        """获取接口统计信息"""
        elapsed = time.time() - self.start_time if self.start_time else 0
        
        return {
            'interface': self.interface_name,
            'is_capturing': self.is_capturing,
            'packet_count': self.packet_count,
            'flow_count': self.flow_count,
            'elapsed_time': elapsed,
            'packet_rate': self.packet_count / elapsed if elapsed > 0 else 0
        }
    
    def get_available_interfaces(self) -> List[str]:
        """获取可用的网络接口列表"""
        try:
            return get_if_list()
        except Exception as e:
            self.logger.error(f"获取网络接口列表失败: {e}")
            return []
