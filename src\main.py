"""
主应用程序入口
工业互联网流量精细化分类管控系统
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.presentation.login_window import LoginWindow
from src.presentation.main_window import MainWindow
from src.infrastructure.config.config_manager import ConfigManager
from src.infrastructure.database.database_manager import DatabaseManager
from src.business_logic.auth.user_auth_manager import UserAuthManager


class Application:
    """主应用程序类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.app = None
        self.login_window = None
        self.main_window = None
        self.config_manager = None
        self.database = None
        self.auth_manager = None
        self.splash = None
        
        # 当前用户会话
        self.current_session_id = None
        self.current_username = None
        
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
    
    def setup_logging(self):
        """设置日志系统"""
        try:
            # 确保日志目录存在
            log_dir = "logs"
            os.makedirs(log_dir, exist_ok=True)
            
            # 配置日志格式
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            
            # 配置根日志记录器
            logging.basicConfig(
                level=logging.INFO,
                format=log_format,
                handlers=[
                    logging.FileHandler(os.path.join(log_dir, 'system.log'), encoding='utf-8'),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            
            # 设置第三方库的日志级别
            logging.getLogger('matplotlib').setLevel(logging.WARNING)
            logging.getLogger('PIL').setLevel(logging.WARNING)
            
        except Exception as e:
            print(f"日志系统初始化失败: {e}")
    
    def create_splash_screen(self):
        """创建启动画面"""
        try:
            # 创建简单的启动画面
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.white)
            
            self.splash = QSplashScreen(splash_pixmap)
            self.splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
            
            # 显示启动信息
            self.splash.showMessage(
                "工业互联网流量精细化分类管控系统\n\n正在初始化...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            
            self.splash.show()
            self.app.processEvents()
            
        except Exception as e:
            self.logger.error(f"启动画面创建失败: {e}")
    
    def init_core_components(self):
        """初始化核心组件"""
        try:
            self.splash.showMessage(
                "工业互联网流量精细化分类管控系统\n\n正在初始化配置管理器...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            self.app.processEvents()
            
            # 初始化配置管理器
            self.config_manager = ConfigManager()
            
            self.splash.showMessage(
                "工业互联网流量精细化分类管控系统\n\n正在初始化数据库...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            self.app.processEvents()
            
            # 初始化数据库
            db_config = self.config_manager.get_database_config()
            self.database = DatabaseManager(db_config.db_path)
            
            self.splash.showMessage(
                "工业互联网流量精细化分类管控系统\n\n正在初始化认证管理器...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            self.app.processEvents()
            
            # 初始化认证管理器
            self.auth_manager = UserAuthManager(self.database)
            
            self.logger.info("核心组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"核心组件初始化失败: {e}")
            QMessageBox.critical(None, "初始化错误", f"系统初始化失败:\n{e}")
            sys.exit(1)
    
    def create_login_window(self):
        """创建登录窗口"""
        try:
            self.splash.showMessage(
                "工业互联网流量精细化分类管控系统\n\n正在创建登录界面...",
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            self.app.processEvents()
            
            self.login_window = LoginWindow()
            
            # 连接登录成功信号
            self.login_window.login_success.connect(self.on_login_success)
            
            self.logger.info("登录窗口创建完成")
            
        except Exception as e:
            self.logger.error(f"登录窗口创建失败: {e}")
            QMessageBox.critical(None, "界面错误", f"登录界面创建失败:\n{e}")
            sys.exit(1)
    
    def on_login_success(self, session_id: str, username: str):
        """登录成功处理"""
        try:
            self.current_session_id = session_id
            self.current_username = username
            
            self.logger.info(f"用户登录成功: {username}")
            
            # 创建主窗口
            self.create_main_window()
            
        except Exception as e:
            self.logger.error(f"登录成功处理失败: {e}")
            QMessageBox.critical(None, "错误", f"登录处理失败:\n{e}")
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            self.main_window = MainWindow(
                self.current_session_id,
                self.current_username,
                self.auth_manager
            )
            
            # 显示主窗口
            self.main_window.show()
            
            # 隐藏登录窗口
            if self.login_window:
                self.login_window.hide()
            
            self.logger.info("主窗口创建完成")
            
        except Exception as e:
            self.logger.error(f"主窗口创建失败: {e}")
            QMessageBox.critical(None, "界面错误", f"主界面创建失败:\n{e}")
    
    def setup_app_properties(self):
        """设置应用程序属性"""
        try:
            # 设置应用程序信息
            self.app.setApplicationName("工业互联网流量管控系统")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("Industrial IoT Team")
            self.app.setOrganizationDomain("industrial-iot.com")
            
            # 设置应用程序图标（如果有的话）
            # self.app.setWindowIcon(QIcon("resources/icons/app_icon.png"))
            
            # 设置全局字体
            font = QFont("Microsoft YaHei", 9)
            self.app.setFont(font)
            
            # 设置样式表
            self.app.setStyleSheet("""
                QApplication {
                    font-family: 'Microsoft YaHei';
                }
                
                QMessageBox {
                    font-size: 10px;
                }
                
                QToolTip {
                    background-color: #ffffcc;
                    border: 1px solid #cccccc;
                    padding: 5px;
                    border-radius: 3px;
                    font-size: 9px;
                }
            """)
            
        except Exception as e:
            self.logger.error(f"应用程序属性设置失败: {e}")
    
    def run(self):
        """运行应用程序"""
        try:
            # 创建QApplication实例
            self.app = QApplication(sys.argv)
            
            # 设置应用程序属性
            self.setup_app_properties()
            
            # 创建启动画面
            self.create_splash_screen()
            
            # 初始化核心组件
            self.init_core_components()
            
            # 创建登录窗口
            self.create_login_window()
            
            # 延迟关闭启动画面并显示登录窗口
            QTimer.singleShot(1000, self.show_login_window)
            
            # 运行应用程序主循环
            return self.app.exec_()
            
        except Exception as e:
            self.logger.error(f"应用程序运行失败: {e}")
            if hasattr(self, 'app') and self.app:
                QMessageBox.critical(None, "系统错误", f"应用程序运行失败:\n{e}")
            else:
                print(f"应用程序运行失败: {e}")
            return 1
        
        finally:
            self.cleanup()
    
    def show_login_window(self):
        """显示登录窗口"""
        try:
            if self.splash:
                self.splash.close()
            
            if self.login_window:
                self.login_window.show()
                self.login_window.raise_()
                self.login_window.activateWindow()
            
        except Exception as e:
            self.logger.error(f"显示登录窗口失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("正在清理应用程序资源...")
            
            # 关闭数据库连接
            if self.database:
                self.database.close()
            
            # 清理会话
            if self.auth_manager and self.current_session_id:
                self.auth_manager.logout(self.current_session_id)
            
            self.logger.info("应用程序资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("错误: 需要Python 3.7或更高版本")
            sys.exit(1)
        
        # 创建并运行应用程序
        app = Application()
        exit_code = app.run()
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n应用程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
