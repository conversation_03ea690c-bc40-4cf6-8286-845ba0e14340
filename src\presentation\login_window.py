"""
登录窗口
用户认证界面，包含用户名、密码输入和登录功能
"""

import sys
import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QApplication, QGridLayout, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

from ..business_logic.auth.user_auth_manager import UserAuthManager
from ..infrastructure.database.database_manager import DatabaseManager
from ..infrastructure.config.config_manager import ConfigManager


class LoginThread(QThread):
    """登录验证线程"""
    login_result = pyqtSignal(bool, str)  # 登录结果信号 (成功/失败, 会话ID/错误信息)
    
    def __init__(self, auth_manager, username, password, ip_address="127.0.0.1"):
        super().__init__()
        self.auth_manager = auth_manager
        self.username = username
        self.password = password
        self.ip_address = ip_address
    
    def run(self):
        """执行登录验证"""
        try:
            session_id = self.auth_manager.authenticate(
                self.username, 
                self.password, 
                self.ip_address
            )
            
            if session_id:
                self.login_result.emit(True, session_id)
            else:
                self.login_result.emit(False, "用户名或密码错误")
                
        except Exception as e:
            self.login_result.emit(False, f"登录异常: {str(e)}")


class LoginWindow(QWidget):
    """登录窗口"""
    
    # 登录成功信号
    login_success = pyqtSignal(str, str)  # 会话ID, 用户名
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.database = DatabaseManager()
        self.auth_manager = UserAuthManager(self.database)
        
        # 登录线程
        self.login_thread = None
        
        # 界面组件
        self.username_input = None
        self.password_input = None
        self.login_button = None
        self.remember_checkbox = None
        
        self.init_ui()
        self.setup_styles()
        
        self.logger.info("登录窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("工业互联网流量管控系统 - 登录")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题区域
        title_frame = self.create_title_section()
        main_layout.addWidget(title_frame)
        
        # 登录表单区域
        form_frame = self.create_form_section()
        main_layout.addWidget(form_frame)
        
        # 按钮区域
        button_frame = self.create_button_section()
        main_layout.addWidget(button_frame)
        
        # 底部信息
        info_frame = self.create_info_section()
        main_layout.addWidget(info_frame)
        
        # 弹性空间
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # 居中显示
        self.center_window()
    
    def create_title_section(self) -> QFrame:
        """创建标题区域"""
        frame = QFrame()
        layout = QVBoxLayout()
        
        # 系统标题
        title_label = QLabel("工业互联网流量管控系统")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        
        # 副标题
        subtitle_label = QLabel("Software-Defined Industrial IoT Traffic Control")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Arial", 10))
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        
        frame.setLayout(layout)
        return frame
    
    def create_form_section(self) -> QFrame:
        """创建表单区域"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout()
        layout.setSpacing(15)
        
        # 用户名
        username_label = QLabel("用户名:")
        username_label.setFont(QFont("Microsoft YaHei", 10))
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setFont(QFont("Microsoft YaHei", 10))
        self.username_input.setMinimumHeight(35)
        
        # 密码
        password_label = QLabel("密码:")
        password_label.setFont(QFont("Microsoft YaHei", 10))
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Microsoft YaHei", 10))
        self.password_input.setMinimumHeight(35)
        
        # 记住密码
        self.remember_checkbox = QCheckBox("记住密码")
        self.remember_checkbox.setFont(QFont("Microsoft YaHei", 9))
        
        # 添加到布局
        layout.addWidget(username_label, 0, 0)
        layout.addWidget(self.username_input, 0, 1)
        layout.addWidget(password_label, 1, 0)
        layout.addWidget(self.password_input, 1, 1)
        layout.addWidget(self.remember_checkbox, 2, 1)
        
        frame.setLayout(layout)
        
        # 绑定回车键登录
        self.username_input.returnPressed.connect(self.on_login_clicked)
        self.password_input.returnPressed.connect(self.on_login_clicked)
        
        return frame
    
    def create_button_section(self) -> QFrame:
        """创建按钮区域"""
        frame = QFrame()
        layout = QHBoxLayout()
        
        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        self.login_button.setMinimumHeight(40)
        self.login_button.setMinimumWidth(120)
        self.login_button.clicked.connect(self.on_login_clicked)
        
        # 退出按钮
        exit_button = QPushButton("退出")
        exit_button.setFont(QFont("Microsoft YaHei", 11))
        exit_button.setMinimumHeight(40)
        exit_button.setMinimumWidth(120)
        exit_button.clicked.connect(self.close)
        
        layout.addStretch()
        layout.addWidget(self.login_button)
        layout.addWidget(exit_button)
        layout.addStretch()
        
        frame.setLayout(layout)
        return frame
    
    def create_info_section(self) -> QFrame:
        """创建信息区域"""
        frame = QFrame()
        layout = QVBoxLayout()
        
        # 默认账户信息
        info_label = QLabel("默认管理员账户: admin / admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Microsoft YaHei", 9))
        info_label.setStyleSheet("color: #95a5a6; margin-top: 20px;")
        
        # 版本信息
        version_label = QLabel("版本 1.0.0 | © 2024 Industrial IoT Team")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setFont(QFont("Microsoft YaHei", 8))
        version_label.setStyleSheet("color: #bdc3c7; margin-top: 10px;")
        
        layout.addWidget(info_label)
        layout.addWidget(version_label)
        
        frame.setLayout(layout)
        return frame
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                font-family: 'Microsoft YaHei';
            }
            
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
                background-color: white;
            }
            
            QLineEdit:focus {
                border-color: #3498db;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QPushButton:disabled {
                background-color: #95a5a6;
            }
            
            QCheckBox {
                color: #2c3e50;
            }
            
            QCheckBox::indicator {
                width: 15px;
                height: 15px;
            }
            
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                border-radius: 3px;
                background-color: #3498db;
                image: url(:/icons/check.png);
            }
        """)
    
    def center_window(self):
        """窗口居中"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def on_login_clicked(self):
        """登录按钮点击事件"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # 输入验证
        if not username:
            QMessageBox.warning(self, "输入错误", "请输入用户名")
            self.username_input.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "输入错误", "请输入密码")
            self.password_input.setFocus()
            return
        
        # 禁用登录按钮，防止重复点击
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        
        # 创建登录线程
        self.login_thread = LoginThread(
            self.auth_manager,
            username,
            password,
            "127.0.0.1"  # 本地IP，实际应用中可以获取真实IP
        )
        
        # 连接信号
        self.login_thread.login_result.connect(self.on_login_result)
        
        # 启动线程
        self.login_thread.start()
    
    @pyqtSlot(bool, str)
    def on_login_result(self, success: bool, result: str):
        """登录结果处理"""
        # 恢复登录按钮
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")
        
        if success:
            # 登录成功
            username = self.username_input.text().strip()
            session_id = result
            
            self.logger.info(f"用户登录成功: {username}")
            
            # 发送登录成功信号
            self.login_success.emit(session_id, username)
            
            # 隐藏登录窗口
            self.hide()
            
        else:
            # 登录失败
            error_message = result
            QMessageBox.critical(self, "登录失败", error_message)
            
            # 清空密码输入框
            self.password_input.clear()
            self.password_input.setFocus()
            
            self.logger.warning(f"登录失败: {error_message}")
    
    def reset_form(self):
        """重置表单"""
        self.username_input.clear()
        self.password_input.clear()
        self.remember_checkbox.setChecked(False)
        self.username_input.setFocus()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 清理资源
        if self.login_thread and self.login_thread.isRunning():
            self.login_thread.quit()
            self.login_thread.wait()
        
        if self.database:
            self.database.close()
        
        event.accept()


if __name__ == "__main__":
    # 测试登录窗口
    app = QApplication(sys.argv)
    
    # 设置应用程序图标和信息
    app.setApplicationName("工业互联网流量管控系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Industrial IoT Team")
    
    # 创建并显示登录窗口
    login_window = LoginWindow()
    login_window.show()
    
    sys.exit(app.exec_())
