"""
主窗口
系统的主界面，包含菜单栏、工具栏、状态栏和各功能模块的容器
"""

import sys
import logging
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTabWidget, QMenuBar, QToolBar, QStatusBar,
                            QAction, QMessageBox, QLabel, QSplitter,
                            QDockWidget, QTextEdit, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPixmap

from .widgets.dashboard_widget import DashboardWidget
from .widgets.flow_monitor_widget import FlowMonitorWidget
from .widgets.orchestration_widget import OrchestrationWidget
from .widgets.prediction_widget import PredictionWidget
from .widgets.system_log_widget import SystemLogWidget
from ..business_logic.core.sifa_core import CoreController
from ..business_logic.auth.user_auth_manager import UserAuthManager, Permission
from ..infrastructure.config.config_manager import ConfigManager


class SystemStatusThread(QThread):
    """系统状态更新线程"""
    status_updated = pyqtSignal(dict)
    
    def __init__(self, core_controller):
        super().__init__()
        self.core_controller = core_controller
        self.running = True
    
    def run(self):
        """运行状态更新循环"""
        while self.running:
            try:
                stats = self.core_controller.get_system_stats()
                self.status_updated.emit(stats)
                self.msleep(5000)  # 5秒更新一次
            except Exception as e:
                logging.error(f"状态更新线程异常: {e}")
                self.msleep(10000)  # 出错时等待10秒
    
    def stop(self):
        """停止线程"""
        self.running = False


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self, session_id: str, username: str, auth_manager: UserAuthManager):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.session_id = session_id
        self.username = username
        self.auth_manager = auth_manager
        
        # 获取用户会话信息
        self.user_session = self.auth_manager.validate_session(session_id)
        if not self.user_session:
            QMessageBox.critical(self, "错误", "会话无效，请重新登录")
            self.close()
            return
        
        # 初始化配置和核心控制器
        self.config_manager = ConfigManager()
        self.core_controller = CoreController(self.config_manager)
        
        # 状态更新线程
        self.status_thread = None
        
        # 界面组件
        self.central_widget = None
        self.tab_widget = None
        self.dashboard_widget = None
        self.flow_monitor_widget = None
        self.orchestration_widget = None
        self.prediction_widget = None
        self.system_log_widget = None
        
        # 状态栏组件
        self.status_label = None
        self.user_label = None
        self.time_label = None
        
        # 定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_bar)
        self.status_timer.start(1000)  # 每秒更新状态栏
        
        self.init_ui()
        self.setup_connections()
        self.start_system_monitoring()
        
        self.logger.info(f"主窗口初始化完成，用户: {username}")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"工业互联网流量管控系统 - {self.username}")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建中央窗口
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建停靠窗口
        self.create_dock_widgets()
        
        # 设置样式
        self.setup_styles()
        
        # 居中显示
        self.center_window()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 系统菜单
        system_menu = menubar.addMenu("系统(&S)")
        
        # 启动监控
        start_action = QAction("启动监控(&S)", self)
        start_action.setShortcut("Ctrl+S")
        start_action.triggered.connect(self.start_monitoring)
        if self.auth_manager.check_permission(self.session_id, Permission.START_MONITORING):
            system_menu.addAction(start_action)
        
        # 停止监控
        stop_action = QAction("停止监控(&T)", self)
        stop_action.setShortcut("Ctrl+T")
        stop_action.triggered.connect(self.stop_monitoring)
        if self.auth_manager.check_permission(self.session_id, Permission.STOP_MONITORING):
            system_menu.addAction(stop_action)
        
        system_menu.addSeparator()
        
        # 系统配置
        config_action = QAction("系统配置(&C)", self)
        config_action.triggered.connect(self.show_config_dialog)
        if self.auth_manager.check_permission(self.session_id, Permission.CONFIG_MANAGEMENT):
            system_menu.addAction(config_action)
        
        system_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        system_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 全屏
        fullscreen_action = QAction("全屏(&F)", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 数据导出
        export_action = QAction("数据导出(&E)", self)
        export_action.triggered.connect(self.export_data)
        if self.auth_manager.check_permission(self.session_id, Permission.EXPORT_DATA):
            tools_menu.addAction(export_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = QToolBar("主工具栏")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.addToolBar(toolbar)
        
        # 启动监控
        if self.auth_manager.check_permission(self.session_id, Permission.START_MONITORING):
            start_action = QAction("启动监控", self)
            start_action.triggered.connect(self.start_monitoring)
            toolbar.addAction(start_action)
        
        # 停止监控
        if self.auth_manager.check_permission(self.session_id, Permission.STOP_MONITORING):
            stop_action = QAction("停止监控", self)
            stop_action.triggered.connect(self.stop_monitoring)
            toolbar.addAction(stop_action)
        
        toolbar.addSeparator()
        
        # 刷新
        refresh_action = QAction("刷新", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
    
    def create_central_widget(self):
        """创建中央窗口"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        layout = QVBoxLayout()
        
        # 创建选项卡窗口
        self.tab_widget = QTabWidget()
        
        # 仪表盘
        if self.auth_manager.check_permission(self.session_id, Permission.VIEW_DASHBOARD):
            self.dashboard_widget = DashboardWidget(self.core_controller)
            self.tab_widget.addTab(self.dashboard_widget, "仪表盘")
        
        # 流量监控
        if self.auth_manager.check_permission(self.session_id, Permission.VIEW_FLOWS):
            self.flow_monitor_widget = FlowMonitorWidget(self.core_controller)
            self.tab_widget.addTab(self.flow_monitor_widget, "流量监控")
        
        # 资源编排
        if self.auth_manager.check_permission(self.session_id, Permission.CREATE_POLICY):
            self.orchestration_widget = OrchestrationWidget(self.core_controller, self.auth_manager, self.session_id)
            self.tab_widget.addTab(self.orchestration_widget, "资源编排")
        
        # 性能预测
        if self.auth_manager.check_permission(self.session_id, Permission.VIEW_PREDICTIONS):
            self.prediction_widget = PredictionWidget(self.core_controller)
            self.tab_widget.addTab(self.prediction_widget, "性能预测")
        
        layout.addWidget(self.tab_widget)
        self.central_widget.setLayout(layout)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # 系统状态标签
        self.status_label = QLabel("系统就绪")
        status_bar.addWidget(self.status_label)
        
        # 分隔符
        status_bar.addPermanentWidget(QLabel("|"))
        
        # 用户信息标签
        self.user_label = QLabel(f"用户: {self.username} ({self.user_session.role.value})")
        status_bar.addPermanentWidget(self.user_label)
        
        # 分隔符
        status_bar.addPermanentWidget(QLabel("|"))
        
        # 时间标签
        self.time_label = QLabel()
        status_bar.addPermanentWidget(self.time_label)
    
    def create_dock_widgets(self):
        """创建停靠窗口"""
        # 系统日志停靠窗口
        log_dock = QDockWidget("系统日志", self)
        log_dock.setAllowedAreas(Qt.BottomDockWidgetArea | Qt.RightDockWidgetArea)
        
        self.system_log_widget = SystemLogWidget()
        log_dock.setWidget(self.system_log_widget)
        
        self.addDockWidget(Qt.BottomDockWidgetArea, log_dock)
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            
            QTabWidget::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background-color: #e1e1e1;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
            
            QTabBar::tab:hover {
                background-color: #f0f0f0;
            }
            
            QStatusBar {
                background-color: #e8e8e8;
                border-top: 1px solid #c0c0c0;
            }
            
            QToolBar {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                spacing: 3px;
            }
            
            QMenuBar {
                background-color: #f0f0f0;
                border-bottom: 1px solid #c0c0c0;
            }
            
            QMenuBar::item {
                padding: 4px 8px;
                background-color: transparent;
            }
            
            QMenuBar::item:selected {
                background-color: #d0d0d0;
            }
        """)
    
    def center_window(self):
        """窗口居中"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def setup_connections(self):
        """设置信号连接"""
        # 连接核心控制器的信号
        self.core_controller.flow_detected.connect(self.on_flow_detected)
        self.core_controller.topology_updated.connect(self.on_topology_updated)
        self.core_controller.prediction_updated.connect(self.on_prediction_updated)
        self.core_controller.orchestration_completed.connect(self.on_orchestration_completed)
        self.core_controller.system_alert.connect(self.on_system_alert)
    
    def start_system_monitoring(self):
        """启动系统监控"""
        # 启动状态更新线程
        self.status_thread = SystemStatusThread(self.core_controller)
        self.status_thread.status_updated.connect(self.on_status_updated)
        self.status_thread.start()
    
    @pyqtSlot()
    def start_monitoring(self):
        """启动监控"""
        try:
            self.core_controller.start_monitoring()
            self.status_label.setText("监控运行中")
            self.system_log_widget.add_log("INFO", "系统监控已启动")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动监控失败: {e}")
            self.logger.error(f"启动监控失败: {e}")
    
    @pyqtSlot()
    def stop_monitoring(self):
        """停止监控"""
        try:
            self.core_controller.stop_monitoring()
            self.status_label.setText("监控已停止")
            self.system_log_widget.add_log("INFO", "系统监控已停止")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止监控失败: {e}")
            self.logger.error(f"停止监控失败: {e}")
    
    @pyqtSlot()
    def refresh_data(self):
        """刷新数据"""
        try:
            # 刷新各个组件的数据
            if self.dashboard_widget:
                self.dashboard_widget.refresh_data()
            if self.flow_monitor_widget:
                self.flow_monitor_widget.refresh_data()
            if self.orchestration_widget:
                self.orchestration_widget.refresh_data()
            if self.prediction_widget:
                self.prediction_widget.refresh_data()
            
            self.system_log_widget.add_log("INFO", "数据刷新完成")
        except Exception as e:
            self.logger.error(f"数据刷新失败: {e}")
    
    @pyqtSlot()
    def show_config_dialog(self):
        """显示配置对话框"""
        QMessageBox.information(self, "配置", "配置功能开发中...")
    
    @pyqtSlot()
    def export_data(self):
        """导出数据"""
        QMessageBox.information(self, "导出", "数据导出功能开发中...")
    
    @pyqtSlot()
    def toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    @pyqtSlot()
    def show_about_dialog(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "工业互联网流量精细化分类管控系统\n\n"
                         "版本: 1.0.0\n"
                         "基于SIFA架构的智能流量管控平台\n\n"
                         "© 2024 Industrial IoT Team")
    
    @pyqtSlot()
    def update_status_bar(self):
        """更新状态栏"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    @pyqtSlot(dict)
    def on_flow_detected(self, flow_data):
        """流量检测事件"""
        if self.flow_monitor_widget:
            self.flow_monitor_widget.add_flow(flow_data)
    
    @pyqtSlot(dict)
    def on_topology_updated(self, topology_data):
        """拓扑更新事件"""
        if self.dashboard_widget:
            self.dashboard_widget.update_topology(topology_data)
    
    @pyqtSlot(dict)
    def on_prediction_updated(self, prediction_data):
        """预测更新事件"""
        if self.prediction_widget:
            self.prediction_widget.update_prediction(prediction_data)
        if self.dashboard_widget:
            self.dashboard_widget.update_kpis(prediction_data)
    
    @pyqtSlot(dict)
    def on_orchestration_completed(self, orchestration_data):
        """编排完成事件"""
        if self.orchestration_widget:
            self.orchestration_widget.add_orchestration_result(orchestration_data)
    
    @pyqtSlot(str, str)
    def on_system_alert(self, level, message):
        """系统告警事件"""
        self.system_log_widget.add_log(level, message)
        
        # 严重告警弹出提示
        if level == "CRITICAL":
            QMessageBox.critical(self, "严重告警", message)
        elif level == "WARNING":
            QMessageBox.warning(self, "警告", message)
    
    @pyqtSlot(dict)
    def on_status_updated(self, stats):
        """状态更新事件"""
        if self.dashboard_widget:
            self.dashboard_widget.update_stats(stats)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止监控
            if self.core_controller.is_running:
                self.core_controller.stop_monitoring()
            
            # 停止状态线程
            if self.status_thread:
                self.status_thread.stop()
                self.status_thread.quit()
                self.status_thread.wait()
            
            # 用户登出
            self.auth_manager.logout(self.session_id)
            
            self.logger.info(f"用户 {self.username} 退出系统")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"窗口关闭异常: {e}")
            event.accept()
