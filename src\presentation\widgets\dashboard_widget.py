"""
仪表盘组件
显示系统总体状态、关键性能指标和实时网络拓扑
"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QProgressBar, QGroupBox,
                            QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QPalette, QColor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np


class KPICard(QFrame):
    """KPI指标卡片"""
    
    def __init__(self, title: str, value: str = "0", unit: str = "", color: str = "#3498db"):
        super().__init__()
        self.title = title
        self.color = color
        self.init_ui()
        self.update_value(value, unit)
    
    def init_ui(self):
        """初始化界面"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedSize(200, 120)
        
        layout = QVBoxLayout()
        layout.setSpacing(5)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # 标题
        self.title_label = QLabel(self.title)
        self.title_label.setFont(QFont("Microsoft YaHei", 10))
        self.title_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
        self.title_label.setAlignment(Qt.AlignCenter)
        
        # 数值
        self.value_label = QLabel("0")
        self.value_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.value_label.setStyleSheet(f"color: {self.color};")
        self.value_label.setAlignment(Qt.AlignCenter)
        
        # 单位
        self.unit_label = QLabel("")
        self.unit_label.setFont(QFont("Microsoft YaHei", 9))
        self.unit_label.setStyleSheet("color: #95a5a6;")
        self.unit_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(self.title_label)
        layout.addWidget(self.value_label)
        layout.addWidget(self.unit_label)
        
        self.setLayout(layout)
        
        # 设置样式
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {self.color};
                border-radius: 8px;
            }}
            QFrame:hover {{
                border-color: {self._darken_color(self.color)};
                background-color: #f8f9fa;
            }}
        """)
    
    def update_value(self, value: str, unit: str = ""):
        """更新数值"""
        self.value_label.setText(str(value))
        self.unit_label.setText(unit)
    
    def _darken_color(self, color: str) -> str:
        """颜色加深"""
        color_map = {
            "#3498db": "#2980b9",
            "#e74c3c": "#c0392b",
            "#2ecc71": "#27ae60",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad"
        }
        return color_map.get(color, color)


class NetworkTopologyWidget(QWidget):
    """网络拓扑图组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 创建matplotlib图形
        self.figure = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        layout.addWidget(self.canvas)
        self.setLayout(layout)
        
        # 初始化拓扑图
        self.init_topology()
    
    def init_topology(self):
        """初始化拓扑图"""
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            
            # 模拟网络拓扑
            nodes = {
                'Controller': (0.5, 0.9),
                'Switch1': (0.2, 0.6),
                'Switch2': (0.8, 0.6),
                'Host1': (0.1, 0.3),
                'Host2': (0.3, 0.3),
                'Host3': (0.7, 0.3),
                'Host4': (0.9, 0.3)
            }
            
            edges = [
                ('Controller', 'Switch1'),
                ('Controller', 'Switch2'),
                ('Switch1', 'Host1'),
                ('Switch1', 'Host2'),
                ('Switch2', 'Host3'),
                ('Switch2', 'Host4'),
                ('Switch1', 'Switch2')
            ]
            
            # 绘制边
            for edge in edges:
                x1, y1 = nodes[edge[0]]
                x2, y2 = nodes[edge[1]]
                ax.plot([x1, x2], [y1, y2], 'b-', linewidth=2, alpha=0.7)
            
            # 绘制节点
            for node, (x, y) in nodes.items():
                if 'Controller' in node:
                    color = '#e74c3c'
                    size = 800
                elif 'Switch' in node:
                    color = '#3498db'
                    size = 600
                else:
                    color = '#2ecc71'
                    size = 400
                
                ax.scatter(x, y, c=color, s=size, alpha=0.8, edgecolors='white', linewidth=2)
                ax.annotate(node, (x, y), xytext=(5, 5), textcoords='offset points',
                           fontsize=9, fontweight='bold')
            
            ax.set_xlim(-0.1, 1.1)
            ax.set_ylim(0.1, 1.0)
            ax.set_aspect('equal')
            ax.axis('off')
            ax.set_title('网络拓扑图', fontsize=14, fontweight='bold', pad=20)
            
            self.canvas.draw()
            
        except Exception as e:
            self.logger.error(f"拓扑图初始化失败: {e}")
    
    def update_topology(self, topology_data):
        """更新拓扑图"""
        try:
            # 这里可以根据实际的拓扑数据更新图形
            self.logger.info("拓扑图更新")
            # 暂时重新绘制静态拓扑
            self.init_topology()
        except Exception as e:
            self.logger.error(f"拓扑图更新失败: {e}")


class FlowClassificationChart(QWidget):
    """流量分类饼图"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.init_ui()
        self.flow_data = {'Industrial Control': 0, 'Non-Control': 0}
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 创建matplotlib图形
        self.figure = Figure(figsize=(6, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        
        layout.addWidget(self.canvas)
        self.setLayout(layout)
        
        # 初始化图表
        self.update_chart()
    
    def update_chart(self):
        """更新图表"""
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            
            # 数据
            labels = list(self.flow_data.keys())
            sizes = list(self.flow_data.values())
            colors = ['#e74c3c', '#3498db']
            
            # 如果没有数据，显示默认图表
            if sum(sizes) == 0:
                sizes = [1, 1]
                labels = ['工业控制流量', '非控制流量']
            
            # 绘制饼图
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors,
                                            autopct='%1.1f%%', startangle=90)
            
            # 设置字体
            for text in texts:
                text.set_fontsize(10)
                text.set_fontweight('bold')
            
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontsize(10)
                autotext.set_fontweight('bold')
            
            ax.set_title('流量分类统计', fontsize=12, fontweight='bold', pad=20)
            
            self.canvas.draw()
            
        except Exception as e:
            self.logger.error(f"流量分类图表更新失败: {e}")
    
    def update_flow_data(self, industrial_count: int, non_control_count: int):
        """更新流量数据"""
        self.flow_data['Industrial Control'] = industrial_count
        self.flow_data['Non-Control'] = non_control_count
        self.update_chart()


class DashboardWidget(QWidget):
    """仪表盘主组件"""
    
    def __init__(self, core_controller):
        super().__init__()
        self.core_controller = core_controller
        self.logger = logging.getLogger(__name__)
        
        # KPI卡片
        self.kpi_cards = {}
        
        # 图表组件
        self.topology_widget = None
        self.flow_chart_widget = None
        
        self.init_ui()
        
        # 定时刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(5000)  # 5秒刷新一次
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # KPI指标区域
        kpi_group = self.create_kpi_section()
        main_layout.addWidget(kpi_group)
        
        # 图表区域
        charts_layout = QHBoxLayout()
        
        # 网络拓扑
        topology_group = QGroupBox("网络拓扑")
        topology_group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        topology_layout = QVBoxLayout()
        self.topology_widget = NetworkTopologyWidget()
        topology_layout.addWidget(self.topology_widget)
        topology_group.setLayout(topology_layout)
        
        # 流量分类
        flow_group = QGroupBox("流量分类")
        flow_group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        flow_layout = QVBoxLayout()
        self.flow_chart_widget = FlowClassificationChart()
        flow_layout.addWidget(self.flow_chart_widget)
        flow_group.setLayout(flow_layout)
        
        charts_layout.addWidget(topology_group, 2)
        charts_layout.addWidget(flow_group, 1)
        
        main_layout.addLayout(charts_layout)
        
        self.setLayout(main_layout)
    
    def create_kpi_section(self) -> QGroupBox:
        """创建KPI指标区域"""
        group = QGroupBox("关键性能指标")
        group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # 创建KPI卡片
        self.kpi_cards['total_flows'] = KPICard("总流量数", "0", "个", "#3498db")
        self.kpi_cards['industrial_flows'] = KPICard("工业流量", "0", "个", "#e74c3c")
        self.kpi_cards['avg_latency'] = KPICard("平均延迟", "0", "ms", "#f39c12")
        self.kpi_cards['packet_loss'] = KPICard("丢包率", "0", "%", "#9b59b6")
        self.kpi_cards['throughput'] = KPICard("吞吐量", "0", "Mbps", "#2ecc71")
        
        for card in self.kpi_cards.values():
            layout.addWidget(card)
        
        layout.addStretch()
        group.setLayout(layout)
        
        return group
    
    @pyqtSlot()
    def refresh_data(self):
        """刷新数据"""
        try:
            # 获取系统统计信息
            stats = self.core_controller.get_system_stats()
            self.update_stats(stats)
            
        except Exception as e:
            self.logger.error(f"仪表盘数据刷新失败: {e}")
    
    def update_stats(self, stats: dict):
        """更新统计信息"""
        try:
            # 更新KPI卡片
            self.kpi_cards['total_flows'].update_value(stats.get('total_flows', 0))
            self.kpi_cards['industrial_flows'].update_value(stats.get('industrial_flows', 0))
            
            # 更新流量分类图表
            if self.flow_chart_widget:
                industrial_count = stats.get('industrial_flows', 0)
                total_count = stats.get('total_flows', 0)
                non_control_count = total_count - industrial_count
                self.flow_chart_widget.update_flow_data(industrial_count, non_control_count)
            
        except Exception as e:
            self.logger.error(f"统计信息更新失败: {e}")
    
    def update_kpis(self, prediction_data: dict):
        """更新KPI数据"""
        try:
            # 从预测数据中提取KPI
            latency = prediction_data.get('latency', '0ms').replace('ms', '')
            packet_loss = prediction_data.get('packet_loss', '0%').replace('%', '')
            throughput = prediction_data.get('throughput', '0Mbps').replace('Mbps', '')
            
            self.kpi_cards['avg_latency'].update_value(latency, "ms")
            self.kpi_cards['packet_loss'].update_value(packet_loss, "%")
            self.kpi_cards['throughput'].update_value(throughput, "Mbps")
            
        except Exception as e:
            self.logger.error(f"KPI更新失败: {e}")
    
    def update_topology(self, topology_data: dict):
        """更新拓扑图"""
        if self.topology_widget:
            self.topology_widget.update_topology(topology_data)
