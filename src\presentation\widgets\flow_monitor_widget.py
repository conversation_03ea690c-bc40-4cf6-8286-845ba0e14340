"""
流量监控组件
实时显示捕获的网络流量信息和分类结果
"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QLabel, QPushButton,
                            QLineEdit, QComboBox, QGroupBox, QSplitter,
                            QTextEdit, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QColor
from datetime import datetime


class FlowTableWidget(QTableWidget):
    """流量表格组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.init_table()
        
        # 最大显示行数
        self.max_rows = 1000
    
    def init_table(self):
        """初始化表格"""
        # 设置列
        columns = ['时间', '源IP', '目标IP', '源端口', '目标端口', '协议', 
                  '数据包大小', '分类结果', 'FEC值', '是否工业流量']
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSortingEnabled(True)
        
        # 设置列宽
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 120)  # 时间
        header.resizeSection(1, 120)  # 源IP
        header.resizeSection(2, 120)  # 目标IP
        header.resizeSection(3, 80)   # 源端口
        header.resizeSection(4, 80)   # 目标端口
        header.resizeSection(5, 60)   # 协议
        header.resizeSection(6, 100)  # 数据包大小
        header.resizeSection(7, 150)  # 分类结果
        header.resizeSection(8, 80)   # FEC值
        
        # 设置样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 5px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
    
    def add_flow(self, flow_data: dict):
        """添加流量记录"""
        try:
            # 检查行数限制
            if self.rowCount() >= self.max_rows:
                self.removeRow(0)  # 删除最旧的记录
            
            # 插入新行
            row = self.rowCount()
            self.insertRow(row)
            
            # 格式化时间
            timestamp = flow_data.get('timestamp', 0)
            time_str = datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]
            
            # 填充数据
            items = [
                time_str,
                flow_data.get('src_ip', ''),
                flow_data.get('dst_ip', ''),
                str(flow_data.get('src_port', '')),
                str(flow_data.get('dst_port', '')),
                flow_data.get('protocol', ''),
                str(flow_data.get('packet_size', '')),
                flow_data.get('classification', ''),
                f"{flow_data.get('fec_score', 0):.2f}",
                '是' if flow_data.get('is_deterministic', False) else '否'
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                
                # 根据分类结果设置颜色
                if 'Industrial Control' in flow_data.get('classification', ''):
                    item.setBackground(QColor('#ffe6e6'))  # 浅红色背景
                elif flow_data.get('is_deterministic', False):
                    item.setBackground(QColor('#e6f3ff'))  # 浅蓝色背景
                
                self.setItem(row, col, item)
            
            # 滚动到最新记录
            self.scrollToBottom()
            
        except Exception as e:
            self.logger.error(f"添加流量记录失败: {e}")
    
    def clear_flows(self):
        """清空流量记录"""
        self.setRowCount(0)
    
    def export_flows(self):
        """导出流量数据"""
        # TODO: 实现导出功能
        pass


class FlowFilterWidget(QFrame):
    """流量过滤组件"""
    
    def __init__(self, flow_table: FlowTableWidget):
        super().__init__()
        self.flow_table = flow_table
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumHeight(80)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # IP过滤
        layout.addWidget(QLabel("IP地址:"))
        self.ip_filter = QLineEdit()
        self.ip_filter.setPlaceholderText("输入IP地址过滤")
        self.ip_filter.setMaximumWidth(150)
        layout.addWidget(self.ip_filter)
        
        # 协议过滤
        layout.addWidget(QLabel("协议:"))
        self.protocol_filter = QComboBox()
        self.protocol_filter.addItems(['全部', 'TCP', 'UDP', 'ICMP'])
        self.protocol_filter.setMaximumWidth(100)
        layout.addWidget(self.protocol_filter)
        
        # 分类过滤
        layout.addWidget(QLabel("分类:"))
        self.classification_filter = QComboBox()
        self.classification_filter.addItems(['全部', '工业控制', '非控制'])
        self.classification_filter.setMaximumWidth(120)
        layout.addWidget(self.classification_filter)
        
        # 按钮
        self.filter_button = QPushButton("应用过滤")
        self.filter_button.clicked.connect(self.apply_filter)
        layout.addWidget(self.filter_button)
        
        self.clear_button = QPushButton("清除过滤")
        self.clear_button.clicked.connect(self.clear_filter)
        layout.addWidget(self.clear_button)
        
        layout.addStretch()
        
        # 清空按钮
        self.clear_table_button = QPushButton("清空表格")
        self.clear_table_button.clicked.connect(self.flow_table.clear_flows)
        layout.addWidget(self.clear_table_button)
        
        self.setLayout(layout)
    
    def apply_filter(self):
        """应用过滤条件"""
        # TODO: 实现过滤逻辑
        pass
    
    def clear_filter(self):
        """清除过滤条件"""
        self.ip_filter.clear()
        self.protocol_filter.setCurrentIndex(0)
        self.classification_filter.setCurrentIndex(0)


class FlowDetailsWidget(QTextEdit):
    """流量详情组件"""
    
    def __init__(self):
        super().__init__()
        self.setMaximumHeight(200)
        self.setReadOnly(True)
        self.setPlaceholderText("选择流量记录查看详细信息...")
        
        # 设置样式
        self.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
    
    def show_flow_details(self, flow_data: dict):
        """显示流量详情"""
        try:
            details = []
            details.append("=== 流量详细信息 ===")
            details.append(f"时间戳: {datetime.fromtimestamp(flow_data.get('timestamp', 0))}")
            details.append(f"源地址: {flow_data.get('src_ip', '')}:{flow_data.get('src_port', '')}")
            details.append(f"目标地址: {flow_data.get('dst_ip', '')}:{flow_data.get('dst_port', '')}")
            details.append(f"协议: {flow_data.get('protocol', '')}")
            details.append(f"数据包大小: {flow_data.get('packet_size', 0)} 字节")
            details.append(f"分类结果: {flow_data.get('classification', '')}")
            details.append(f"FEC值: {flow_data.get('fec_score', 0):.4f}")
            details.append(f"工业流量: {'是' if flow_data.get('is_deterministic', False) else '否'}")
            
            # 特征信息
            features = flow_data.get('features', {})
            if features:
                details.append("\n=== 流量特征 ===")
                for key, value in features.items():
                    details.append(f"{key}: {value}")
            
            self.setText('\n'.join(details))
            
        except Exception as e:
            self.setText(f"显示详情失败: {e}")


class FlowMonitorWidget(QWidget):
    """流量监控主组件"""
    
    def __init__(self, core_controller):
        super().__init__()
        self.core_controller = core_controller
        self.logger = logging.getLogger(__name__)
        
        # 组件
        self.flow_table = None
        self.filter_widget = None
        self.details_widget = None
        
        # 统计信息
        self.flow_stats = {
            'total_count': 0,
            'industrial_count': 0,
            'last_update': None
        }
        
        self.init_ui()
        
        # 定时更新统计
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats_display)
        self.stats_timer.start(2000)  # 2秒更新一次
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 统计信息区域
        stats_group = self.create_stats_section()
        main_layout.addWidget(stats_group)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 上半部分：过滤器和表格
        top_widget = QWidget()
        top_layout = QVBoxLayout()
        
        # 过滤器
        self.flow_table = FlowTableWidget()
        self.filter_widget = FlowFilterWidget(self.flow_table)
        top_layout.addWidget(self.filter_widget)
        
        # 流量表格
        top_layout.addWidget(self.flow_table)
        top_widget.setLayout(top_layout)
        
        # 下半部分：详情显示
        self.details_widget = FlowDetailsWidget()
        
        splitter.addWidget(top_widget)
        splitter.addWidget(self.details_widget)
        splitter.setSizes([600, 200])
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        
        # 连接信号
        self.flow_table.itemSelectionChanged.connect(self.on_selection_changed)
    
    def create_stats_section(self) -> QGroupBox:
        """创建统计信息区域"""
        group = QGroupBox("流量统计")
        group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        group.setMaximumHeight(80)
        
        layout = QHBoxLayout()
        
        # 统计标签
        self.total_label = QLabel("总流量: 0")
        self.total_label.setFont(QFont("Microsoft YaHei", 10))
        
        self.industrial_label = QLabel("工业流量: 0")
        self.industrial_label.setFont(QFont("Microsoft YaHei", 10))
        self.industrial_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        
        self.ratio_label = QLabel("工业流量比例: 0%")
        self.ratio_label.setFont(QFont("Microsoft YaHei", 10))
        
        self.rate_label = QLabel("流量速率: 0 pps")
        self.rate_label.setFont(QFont("Microsoft YaHei", 10))
        
        layout.addWidget(self.total_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.industrial_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.ratio_label)
        layout.addWidget(QLabel("|"))
        layout.addWidget(self.rate_label)
        layout.addStretch()
        
        group.setLayout(layout)
        return group
    
    @pyqtSlot()
    def on_selection_changed(self):
        """表格选择变化事件"""
        try:
            current_row = self.flow_table.currentRow()
            if current_row >= 0:
                # 获取选中行的数据
                row_data = {}
                for col in range(self.flow_table.columnCount()):
                    item = self.flow_table.item(current_row, col)
                    if item:
                        header = self.flow_table.horizontalHeaderItem(col).text()
                        row_data[header] = item.text()
                
                # 构造流量数据字典（简化版）
                flow_data = {
                    'timestamp': datetime.now().timestamp(),
                    'src_ip': row_data.get('源IP', ''),
                    'dst_ip': row_data.get('目标IP', ''),
                    'src_port': row_data.get('源端口', ''),
                    'dst_port': row_data.get('目标端口', ''),
                    'protocol': row_data.get('协议', ''),
                    'packet_size': row_data.get('数据包大小', ''),
                    'classification': row_data.get('分类结果', ''),
                    'fec_score': float(row_data.get('FEC值', '0')),
                    'is_deterministic': row_data.get('是否工业流量', '') == '是'
                }
                
                self.details_widget.show_flow_details(flow_data)
                
        except Exception as e:
            self.logger.error(f"选择变化处理失败: {e}")
    
    def add_flow(self, flow_data: dict):
        """添加流量记录"""
        try:
            self.flow_table.add_flow(flow_data)
            
            # 更新统计
            self.flow_stats['total_count'] += 1
            if flow_data.get('is_deterministic', False):
                self.flow_stats['industrial_count'] += 1
            self.flow_stats['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"添加流量失败: {e}")
    
    def update_stats_display(self):
        """更新统计显示"""
        try:
            total = self.flow_stats['total_count']
            industrial = self.flow_stats['industrial_count']
            ratio = (industrial / max(1, total)) * 100
            
            # 计算流量速率（简化计算）
            rate = 0
            if self.flow_stats['last_update']:
                # 这里可以实现更精确的速率计算
                rate = 10  # 模拟值
            
            self.total_label.setText(f"总流量: {total}")
            self.industrial_label.setText(f"工业流量: {industrial}")
            self.ratio_label.setText(f"工业流量比例: {ratio:.1f}%")
            self.rate_label.setText(f"流量速率: {rate} pps")
            
        except Exception as e:
            self.logger.error(f"统计显示更新失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            # 这里可以实现数据刷新逻辑
            self.logger.info("流量监控数据刷新")
        except Exception as e:
            self.logger.error(f"数据刷新失败: {e}")
