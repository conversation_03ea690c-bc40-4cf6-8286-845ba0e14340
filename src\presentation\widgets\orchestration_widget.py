"""
资源编排组件
用于创建、管理和监控资源编排策略
"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QGroupBox, QPushButton, QLabel,
                            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox,
                            QTextEdit, QSplitter, QFormLayout, QFrame,
                            QMessageBox, QDialog, QDialogButtonBox)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QFont


class PolicyCreateDialog(QDialog):
    """策略创建对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("创建编排策略")
        self.setFixedSize(500, 400)
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 表单区域
        form_layout = QFormLayout()
        
        # 策略名称
        self.policy_name = QLineEdit()
        self.policy_name.setPlaceholderText("输入策略名称")
        form_layout.addRow("策略名称:", self.policy_name)
        
        # 目标流量类型
        self.flow_type = QComboBox()
        self.flow_type.addItems(['工业控制流量', '实时通信流量', '视频流量', '文件传输'])
        form_layout.addRow("目标流量:", self.flow_type)
        
        # QoS要求
        qos_group = QGroupBox("QoS要求")
        qos_layout = QFormLayout()
        
        self.latency_req = QDoubleSpinBox()
        self.latency_req.setRange(1.0, 1000.0)
        self.latency_req.setValue(50.0)
        self.latency_req.setSuffix(" ms")
        qos_layout.addRow("延迟上限:", self.latency_req)
        
        self.bandwidth_req = QDoubleSpinBox()
        self.bandwidth_req.setRange(0.1, 1000.0)
        self.bandwidth_req.setValue(10.0)
        self.bandwidth_req.setSuffix(" Mbps")
        qos_layout.addRow("带宽需求:", self.bandwidth_req)
        
        self.packet_loss_req = QDoubleSpinBox()
        self.packet_loss_req.setRange(0.001, 10.0)
        self.packet_loss_req.setValue(1.0)
        self.packet_loss_req.setSuffix(" %")
        qos_layout.addRow("丢包率上限:", self.packet_loss_req)
        
        qos_group.setLayout(qos_layout)
        
        # PSO参数
        pso_group = QGroupBox("PSO算法参数")
        pso_layout = QFormLayout()
        
        self.population_size = QSpinBox()
        self.population_size.setRange(10, 200)
        self.population_size.setValue(50)
        pso_layout.addRow("种群大小:", self.population_size)
        
        self.max_iterations = QSpinBox()
        self.max_iterations.setRange(10, 500)
        self.max_iterations.setValue(100)
        pso_layout.addRow("最大迭代数:", self.max_iterations)
        
        self.inertia_weight = QDoubleSpinBox()
        self.inertia_weight.setRange(0.1, 1.0)
        self.inertia_weight.setValue(0.9)
        self.inertia_weight.setDecimals(2)
        pso_layout.addRow("惯性权重:", self.inertia_weight)
        
        pso_group.setLayout(pso_layout)
        
        # 添加到主布局
        layout.addLayout(form_layout)
        layout.addWidget(qos_group)
        layout.addWidget(pso_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def get_policy_data(self):
        """获取策略数据"""
        return {
            'policy_name': self.policy_name.text(),
            'target_flow_filter': {
                'flow_type': self.flow_type.currentText()
            },
            'qos_requirements': {
                'latency': self.latency_req.value(),
                'bandwidth': self.bandwidth_req.value(),
                'packet_loss': self.packet_loss_req.value()
            },
            'pso_parameters': {
                'population_size': self.population_size.value(),
                'max_iterations': self.max_iterations.value(),
                'inertia_weight': self.inertia_weight.value()
            }
        }


class PolicyTableWidget(QTableWidget):
    """策略表格组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.init_table()
    
    def init_table(self):
        """初始化表格"""
        columns = ['策略名称', '目标流量', '延迟要求', '带宽要求', '状态', '创建时间', '操作']
        self.setColumnCount(len(columns))
        self.setHorizontalHeaderLabels(columns)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 设置列宽
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 150)  # 策略名称
        header.resizeSection(1, 120)  # 目标流量
        header.resizeSection(2, 100)  # 延迟要求
        header.resizeSection(3, 100)  # 带宽要求
        header.resizeSection(4, 80)   # 状态
        header.resizeSection(5, 150)  # 创建时间
        
        # 设置样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
    
    def add_policy(self, policy_data: dict):
        """添加策略记录"""
        try:
            row = self.rowCount()
            self.insertRow(row)
            
            # 填充数据
            items = [
                policy_data.get('policy_name', ''),
                policy_data.get('target_flow_filter', {}).get('flow_type', ''),
                f"{policy_data.get('qos_requirements', {}).get('latency', 0)} ms",
                f"{policy_data.get('qos_requirements', {}).get('bandwidth', 0)} Mbps",
                policy_data.get('status', 'inactive'),
                policy_data.get('created_at', ''),
                ''  # 操作列
            ]
            
            for col, item_text in enumerate(items[:-1]):  # 除了操作列
                item = QTableWidgetItem(str(item_text))
                self.setItem(row, col, item)
            
            # 操作按钮
            self.create_action_buttons(row)
            
        except Exception as e:
            self.logger.error(f"添加策略记录失败: {e}")
    
    def create_action_buttons(self, row: int):
        """创建操作按钮"""
        button_widget = QWidget()
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(5, 2, 5, 2)
        
        # 启动/停止按钮
        toggle_button = QPushButton("启动")
        toggle_button.setMaximumWidth(60)
        toggle_button.clicked.connect(lambda: self.toggle_policy(row))
        
        # 删除按钮
        delete_button = QPushButton("删除")
        delete_button.setMaximumWidth(60)
        delete_button.clicked.connect(lambda: self.delete_policy(row))
        
        button_layout.addWidget(toggle_button)
        button_layout.addWidget(delete_button)
        button_widget.setLayout(button_layout)
        
        self.setCellWidget(row, 6, button_widget)
    
    def toggle_policy(self, row: int):
        """切换策略状态"""
        try:
            status_item = self.item(row, 4)
            if status_item:
                current_status = status_item.text()
                new_status = 'active' if current_status == 'inactive' else 'inactive'
                status_item.setText(new_status)
                
                # 更新按钮文本
                button_widget = self.cellWidget(row, 6)
                if button_widget:
                    toggle_button = button_widget.findChild(QPushButton)
                    if toggle_button:
                        toggle_button.setText("停止" if new_status == 'active' else "启动")
                
                self.logger.info(f"策略状态切换: {new_status}")
                
        except Exception as e:
            self.logger.error(f"策略状态切换失败: {e}")
    
    def delete_policy(self, row: int):
        """删除策略"""
        try:
            reply = QMessageBox.question(self, "确认删除", "确定要删除这个策略吗？",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.removeRow(row)
                self.logger.info("策略删除成功")
                
        except Exception as e:
            self.logger.error(f"策略删除失败: {e}")


class OrchestrationResultWidget(QTextEdit):
    """编排结果显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setReadOnly(True)
        self.setMaximumHeight(200)
        self.setPlaceholderText("编排结果将在这里显示...")
        
        # 设置样式
        self.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
    
    def add_orchestration_result(self, result_data: dict):
        """添加编排结果"""
        try:
            from datetime import datetime
            
            timestamp = datetime.now().strftime('%H:%M:%S')
            flow_id = result_data.get('flow_id', 'unknown')
            path = ' -> '.join(result_data.get('path', []))
            latency = result_data.get('total_latency', 0)
            fitness = result_data.get('fitness_score', 0)
            
            result_text = f"[{timestamp}] 编排完成\n"
            result_text += f"流量ID: {flow_id}\n"
            result_text += f"路径: {path}\n"
            result_text += f"总延迟: {latency:.2f} ms\n"
            result_text += f"适应度: {fitness:.6f}\n"
            result_text += "-" * 50 + "\n"
            
            # 添加到文本末尾
            self.append(result_text)
            
            # 滚动到底部
            self.moveCursor(self.textCursor().End)
            
        except Exception as e:
            self.append(f"显示编排结果失败: {e}\n")


class OrchestrationWidget(QWidget):
    """资源编排主组件"""
    
    def __init__(self, core_controller, auth_manager, session_id):
        super().__init__()
        self.core_controller = core_controller
        self.auth_manager = auth_manager
        self.session_id = session_id
        self.logger = logging.getLogger(__name__)
        
        # 组件
        self.policy_table = None
        self.result_widget = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.create_button = QPushButton("创建策略")
        self.create_button.clicked.connect(self.create_policy)
        toolbar_layout.addWidget(self.create_button)
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_button)
        
        toolbar_layout.addStretch()
        
        # 统计信息
        self.stats_label = QLabel("策略统计: 总数 0, 激活 0")
        self.stats_label.setFont(QFont("Microsoft YaHei", 10))
        toolbar_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(toolbar_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 策略表格
        policy_group = QGroupBox("编排策略")
        policy_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        policy_layout = QVBoxLayout()
        
        self.policy_table = PolicyTableWidget()
        policy_layout.addWidget(self.policy_table)
        policy_group.setLayout(policy_layout)
        
        # 编排结果
        result_group = QGroupBox("编排结果")
        result_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        result_layout = QVBoxLayout()
        
        self.result_widget = OrchestrationResultWidget()
        result_layout.addWidget(self.result_widget)
        result_group.setLayout(result_layout)
        
        splitter.addWidget(policy_group)
        splitter.addWidget(result_group)
        splitter.setSizes([400, 200])
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        
        # 加载现有策略
        self.load_policies()
    
    @pyqtSlot()
    def create_policy(self):
        """创建新策略"""
        try:
            dialog = PolicyCreateDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                policy_data = dialog.get_policy_data()
                
                # 验证输入
                if not policy_data['policy_name']:
                    QMessageBox.warning(self, "输入错误", "请输入策略名称")
                    return
                
                # 添加创建信息
                from datetime import datetime
                policy_data['status'] = 'inactive'
                policy_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                policy_data['created_by'] = self.session_id
                
                # 保存到数据库
                if hasattr(self.core_controller, 'database') and self.core_controller.database:
                    success = self.core_controller.database.save_policy(policy_data)
                    if success:
                        self.policy_table.add_policy(policy_data)
                        self.update_stats()
                        QMessageBox.information(self, "成功", "策略创建成功")
                    else:
                        QMessageBox.critical(self, "错误", "策略保存失败")
                else:
                    # 如果没有数据库，直接添加到表格
                    self.policy_table.add_policy(policy_data)
                    self.update_stats()
                    QMessageBox.information(self, "成功", "策略创建成功")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建策略失败: {e}")
            self.logger.error(f"创建策略失败: {e}")
    
    def load_policies(self):
        """加载现有策略"""
        try:
            # 从数据库加载策略
            if hasattr(self.core_controller, 'database') and self.core_controller.database:
                policies = self.core_controller.database.get_active_policies()
                for policy in policies:
                    self.policy_table.add_policy(policy)
            
            self.update_stats()
            
        except Exception as e:
            self.logger.error(f"加载策略失败: {e}")
    
    def update_stats(self):
        """更新统计信息"""
        try:
            total_count = self.policy_table.rowCount()
            active_count = 0
            
            for row in range(total_count):
                status_item = self.policy_table.item(row, 4)
                if status_item and status_item.text() == 'active':
                    active_count += 1
            
            self.stats_label.setText(f"策略统计: 总数 {total_count}, 激活 {active_count}")
            
        except Exception as e:
            self.logger.error(f"统计更新失败: {e}")
    
    def add_orchestration_result(self, result_data: dict):
        """添加编排结果"""
        if self.result_widget:
            self.result_widget.add_orchestration_result(result_data)
    
    def refresh_data(self):
        """刷新数据"""
        try:
            # 清空表格并重新加载
            self.policy_table.setRowCount(0)
            self.load_policies()
            self.logger.info("编排策略数据刷新完成")
            
        except Exception as e:
            self.logger.error(f"数据刷新失败: {e}")
