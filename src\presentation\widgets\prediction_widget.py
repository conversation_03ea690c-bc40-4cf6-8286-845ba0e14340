"""
性能预测组件
显示GNN网络性能预测结果和历史趋势
"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QFrame, QGridLayout, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QColor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
from collections import deque
from datetime import datetime, timedelta


class PredictionCard(QFrame):
    """预测指标卡片"""
    
    def __init__(self, title: str, unit: str = "", color: str = "#3498db"):
        super().__init__()
        self.title = title
        self.unit = unit
        self.color = color
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedSize(180, 100)
        
        layout = QVBoxLayout()
        layout.setSpacing(5)
        layout.setContentsMargins(10, 8, 10, 8)
        
        # 标题
        self.title_label = QLabel(self.title)
        self.title_label.setFont(QFont("Microsoft YaHei", 9))
        self.title_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
        self.title_label.setAlignment(Qt.AlignCenter)
        
        # 预测值
        self.prediction_label = QLabel("--")
        self.prediction_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.prediction_label.setStyleSheet(f"color: {self.color};")
        self.prediction_label.setAlignment(Qt.AlignCenter)
        
        # 置信度
        self.confidence_label = QLabel("置信度: --%")
        self.confidence_label.setFont(QFont("Microsoft YaHei", 8))
        self.confidence_label.setStyleSheet("color: #95a5a6;")
        self.confidence_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(self.title_label)
        layout.addWidget(self.prediction_label)
        layout.addWidget(self.confidence_label)
        
        self.setLayout(layout)
        
        # 设置样式
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {self.color};
                border-radius: 6px;
            }}
            QFrame:hover {{
                border-color: {self._darken_color(self.color)};
                background-color: #f8f9fa;
            }}
        """)
    
    def update_prediction(self, value: str, confidence: float = 0.0):
        """更新预测值"""
        self.prediction_label.setText(value)
        self.confidence_label.setText(f"置信度: {confidence:.1%}")
    
    def _darken_color(self, color: str) -> str:
        """颜色加深"""
        color_map = {
            "#3498db": "#2980b9",
            "#e74c3c": "#c0392b", 
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad"
        }
        return color_map.get(color, color)


class PredictionTrendChart(QWidget):
    """预测趋势图"""
    
    def __init__(self, title: str, ylabel: str, color: str = "#3498db"):
        super().__init__()
        self.title = title
        self.ylabel = ylabel
        self.color = color
        self.logger = logging.getLogger(__name__)
        
        # 数据存储
        self.max_points = 50
        self.timestamps = deque(maxlen=self.max_points)
        self.values = deque(maxlen=self.max_points)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建matplotlib图形
        self.figure = Figure(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        layout.addWidget(self.canvas)
        self.setLayout(layout)
        
        # 初始化图表
        self.init_chart()
    
    def init_chart(self):
        """初始化图表"""
        try:
            self.figure.clear()
            self.ax = self.figure.add_subplot(111)
            
            # 设置样式
            self.ax.set_title(self.title, fontsize=12, fontweight='bold', pad=15)
            self.ax.set_ylabel(self.ylabel, fontsize=10)
            self.ax.grid(True, alpha=0.3)
            
            # 初始化空线条
            self.line, = self.ax.plot([], [], color=self.color, linewidth=2, marker='o', markersize=4)
            
            # 设置x轴格式
            self.ax.tick_params(axis='x', rotation=45, labelsize=8)
            self.ax.tick_params(axis='y', labelsize=8)
            
            # 调整布局
            self.figure.tight_layout()
            
            self.canvas.draw()
            
        except Exception as e:
            self.logger.error(f"图表初始化失败: {e}")
    
    def add_data_point(self, timestamp: datetime, value: float):
        """添加数据点"""
        try:
            self.timestamps.append(timestamp)
            self.values.append(value)
            self.update_chart()
            
        except Exception as e:
            self.logger.error(f"添加数据点失败: {e}")
    
    def update_chart(self):
        """更新图表"""
        try:
            if not self.timestamps or not self.values:
                return
            
            # 更新数据
            self.line.set_data(list(self.timestamps), list(self.values))
            
            # 调整坐标轴范围
            if len(self.timestamps) > 1:
                self.ax.set_xlim(self.timestamps[0], self.timestamps[-1])
                
            if self.values:
                y_min, y_max = min(self.values), max(self.values)
                y_range = y_max - y_min
                if y_range > 0:
                    self.ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)
                else:
                    self.ax.set_ylim(y_min - 1, y_max + 1)
            
            # 格式化x轴标签
            if len(self.timestamps) > 10:
                # 只显示部分时间标签
                step = len(self.timestamps) // 5
                ticks = list(self.timestamps)[::step]
                labels = [t.strftime('%H:%M:%S') for t in ticks]
                self.ax.set_xticks(ticks)
                self.ax.set_xticklabels(labels)
            else:
                labels = [t.strftime('%H:%M:%S') for t in self.timestamps]
                self.ax.set_xticklabels(labels)
            
            self.canvas.draw()
            
        except Exception as e:
            self.logger.error(f"图表更新失败: {e}")


class PredictionWidget(QWidget):
    """性能预测主组件"""
    
    def __init__(self, core_controller):
        super().__init__()
        self.core_controller = core_controller
        self.logger = logging.getLogger(__name__)
        
        # 预测卡片
        self.prediction_cards = {}
        
        # 趋势图
        self.trend_charts = {}
        
        # 最新预测数据
        self.latest_prediction = {}
        
        self.init_ui()
        
        # 定时更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.request_prediction_update)
        self.update_timer.start(10000)  # 10秒更新一次
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # 预测指标卡片区域
        cards_group = self.create_prediction_cards_section()
        main_layout.addWidget(cards_group)
        
        # 趋势图区域
        charts_group = self.create_trend_charts_section()
        main_layout.addWidget(charts_group)
        
        self.setLayout(main_layout)
    
    def create_prediction_cards_section(self) -> QGroupBox:
        """创建预测指标卡片区域"""
        group = QGroupBox("实时预测结果")
        group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # 创建预测卡片
        self.prediction_cards['latency'] = PredictionCard("网络延迟", "ms", "#e74c3c")
        self.prediction_cards['packet_loss'] = PredictionCard("丢包率", "%", "#f39c12")
        self.prediction_cards['throughput'] = PredictionCard("吞吐量", "Mbps", "#2ecc71")
        self.prediction_cards['congestion'] = PredictionCard("拥塞级别", "", "#9b59b6")
        
        for card in self.prediction_cards.values():
            layout.addWidget(card)
        
        layout.addStretch()
        group.setLayout(layout)
        
        return group
    
    def create_trend_charts_section(self) -> QGroupBox:
        """创建趋势图区域"""
        group = QGroupBox("预测趋势分析")
        group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        
        layout = QGridLayout()
        layout.setSpacing(10)
        
        # 创建趋势图
        self.trend_charts['latency'] = PredictionTrendChart("延迟预测趋势", "延迟 (ms)", "#e74c3c")
        self.trend_charts['packet_loss'] = PredictionTrendChart("丢包率预测趋势", "丢包率 (%)", "#f39c12")
        self.trend_charts['throughput'] = PredictionTrendChart("吞吐量预测趋势", "吞吐量 (Mbps)", "#2ecc71")
        self.trend_charts['congestion'] = PredictionTrendChart("拥塞级别预测趋势", "拥塞级别", "#9b59b6")
        
        # 2x2布局
        layout.addWidget(self.trend_charts['latency'], 0, 0)
        layout.addWidget(self.trend_charts['packet_loss'], 0, 1)
        layout.addWidget(self.trend_charts['throughput'], 1, 0)
        layout.addWidget(self.trend_charts['congestion'], 1, 1)
        
        group.setLayout(layout)
        
        return group
    
    @pyqtSlot()
    def request_prediction_update(self):
        """请求预测更新"""
        try:
            # 这里可以主动请求预测更新
            # 实际实现中，预测更新通常由核心控制器主动推送
            pass
            
        except Exception as e:
            self.logger.error(f"请求预测更新失败: {e}")
    
    def update_prediction(self, prediction_data: dict):
        """更新预测结果"""
        try:
            self.latest_prediction = prediction_data
            current_time = datetime.now()
            
            # 解析预测数据
            latency_str = prediction_data.get('latency', '0ms')
            packet_loss_str = prediction_data.get('packet_loss', '0%')
            throughput_str = prediction_data.get('throughput', '0Mbps')
            congestion_level = prediction_data.get('congestion_level', 0.0)
            confidence = float(prediction_data.get('confidence', '0.5'))
            
            # 提取数值
            latency_val = float(latency_str.replace('ms', ''))
            packet_loss_val = float(packet_loss_str.replace('%', ''))
            throughput_val = float(throughput_str.replace('Mbps', ''))
            
            # 更新预测卡片
            self.prediction_cards['latency'].update_prediction(latency_str, confidence)
            self.prediction_cards['packet_loss'].update_prediction(packet_loss_str, confidence)
            self.prediction_cards['throughput'].update_prediction(throughput_str, confidence)
            self.prediction_cards['congestion'].update_prediction(f"{congestion_level:.2f}", confidence)
            
            # 更新趋势图
            self.trend_charts['latency'].add_data_point(current_time, latency_val)
            self.trend_charts['packet_loss'].add_data_point(current_time, packet_loss_val)
            self.trend_charts['throughput'].add_data_point(current_time, throughput_val)
            self.trend_charts['congestion'].add_data_point(current_time, congestion_level)
            
            self.logger.debug(f"预测结果更新: 延迟={latency_str}, 丢包率={packet_loss_str}, 吞吐量={throughput_str}")
            
        except Exception as e:
            self.logger.error(f"预测结果更新失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            # 清空趋势图数据
            for chart in self.trend_charts.values():
                chart.timestamps.clear()
                chart.values.clear()
                chart.init_chart()
            
            # 重置预测卡片
            for card in self.prediction_cards.values():
                card.update_prediction("--", 0.0)
            
            self.logger.info("预测数据刷新完成")
            
        except Exception as e:
            self.logger.error(f"数据刷新失败: {e}")
    
    def get_latest_prediction(self) -> dict:
        """获取最新预测结果"""
        return self.latest_prediction.copy()
    
    def export_prediction_data(self):
        """导出预测数据"""
        try:
            # TODO: 实现预测数据导出功能
            self.logger.info("预测数据导出功能开发中")
            
        except Exception as e:
            self.logger.error(f"预测数据导出失败: {e}")
