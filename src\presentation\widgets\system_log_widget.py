"""
系统日志组件
显示系统运行日志和告警信息
"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
                            QPushButton, QComboBox, QLabel, QCheckBox,
                            QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QTextCursor, QColor, QTextCharFormat
from datetime import datetime
from collections import deque


class SystemLogWidget(QWidget):
    """系统日志组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # 日志缓存
        self.max_logs = 1000
        self.log_buffer = deque(maxlen=self.max_logs)
        
        # 过滤设置
        self.level_filter = "ALL"
        self.auto_scroll = True
        
        self.init_ui()
        
        # 定时清理过期日志
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.cleanup_old_logs)
        self.cleanup_timer.start(60000)  # 1分钟清理一次
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 工具栏
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 9))
        self.log_display.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 设置样式
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #3c3c3c;
                border-radius: 4px;
                padding: 5px;
            }
        """)
        
        layout.addWidget(self.log_display)
        self.setLayout(layout)
        
        # 添加初始日志
        self.add_log("INFO", "系统日志组件初始化完成")
    
    def create_toolbar(self) -> QFrame:
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMaximumHeight(40)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 日志级别过滤
        layout.addWidget(QLabel("级别:"))
        self.level_combo = QComboBox()
        self.level_combo.addItems(["ALL", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_combo.setCurrentText("ALL")
        self.level_combo.currentTextChanged.connect(self.on_level_filter_changed)
        self.level_combo.setMaximumWidth(100)
        layout.addWidget(self.level_combo)
        
        # 自动滚动
        self.auto_scroll_checkbox = QCheckBox("自动滚动")
        self.auto_scroll_checkbox.setChecked(True)
        self.auto_scroll_checkbox.toggled.connect(self.on_auto_scroll_toggled)
        layout.addWidget(self.auto_scroll_checkbox)
        
        layout.addStretch()
        
        # 清空按钮
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self.clear_logs)
        self.clear_button.setMaximumWidth(60)
        layout.addWidget(self.clear_button)
        
        # 导出按钮
        self.export_button = QPushButton("导出")
        self.export_button.clicked.connect(self.export_logs)
        self.export_button.setMaximumWidth(60)
        layout.addWidget(self.export_button)
        
        # 日志计数
        self.count_label = QLabel("日志: 0")
        self.count_label.setMinimumWidth(80)
        layout.addWidget(self.count_label)
        
        toolbar.setLayout(layout)
        return toolbar
    
    def add_log(self, level: str, message: str, module: str = "system"):
        """添加日志条目"""
        try:
            timestamp = datetime.now()
            log_entry = {
                'timestamp': timestamp,
                'level': level.upper(),
                'module': module,
                'message': message
            }
            
            # 添加到缓存
            self.log_buffer.append(log_entry)
            
            # 检查过滤条件
            if self.should_display_log(log_entry):
                self.display_log(log_entry)
            
            # 更新计数
            self.update_log_count()
            
        except Exception as e:
            print(f"添加日志失败: {e}")
    
    def should_display_log(self, log_entry: dict) -> bool:
        """检查是否应该显示日志"""
        if self.level_filter == "ALL":
            return True
        
        return log_entry['level'] == self.level_filter
    
    def display_log(self, log_entry: dict):
        """显示日志条目"""
        try:
            # 格式化时间
            time_str = log_entry['timestamp'].strftime('%H:%M:%S.%f')[:-3]
            
            # 格式化日志文本
            log_text = f"[{time_str}] [{log_entry['level']:8}] [{log_entry['module']:10}] {log_entry['message']}"
            
            # 获取颜色
            color = self.get_level_color(log_entry['level'])
            
            # 添加到显示区域
            cursor = self.log_display.textCursor()
            cursor.movePosition(QTextCursor.End)
            
            # 设置文本格式
            format = QTextCharFormat()
            format.setForeground(QColor(color))
            cursor.setCharFormat(format)
            
            # 插入文本
            cursor.insertText(log_text + '\n')
            
            # 自动滚动到底部
            if self.auto_scroll:
                self.log_display.moveCursor(QTextCursor.End)
            
        except Exception as e:
            print(f"显示日志失败: {e}")
    
    def get_level_color(self, level: str) -> str:
        """获取日志级别对应的颜色"""
        color_map = {
            'DEBUG': '#808080',    # 灰色
            'INFO': '#ffffff',     # 白色
            'WARNING': '#ffaa00',  # 橙色
            'ERROR': '#ff4444',    # 红色
            'CRITICAL': '#ff0000'  # 亮红色
        }
        return color_map.get(level, '#ffffff')
    
    @pyqtSlot(str)
    def on_level_filter_changed(self, level: str):
        """日志级别过滤变化"""
        self.level_filter = level
        self.refresh_display()
    
    @pyqtSlot(bool)
    def on_auto_scroll_toggled(self, enabled: bool):
        """自动滚动切换"""
        self.auto_scroll = enabled
    
    def refresh_display(self):
        """刷新显示"""
        try:
            # 清空显示区域
            self.log_display.clear()
            
            # 重新显示符合过滤条件的日志
            for log_entry in self.log_buffer:
                if self.should_display_log(log_entry):
                    self.display_log(log_entry)
            
            # 滚动到底部
            if self.auto_scroll:
                self.log_display.moveCursor(QTextCursor.End)
            
        except Exception as e:
            self.logger.error(f"刷新显示失败: {e}")
    
    @pyqtSlot()
    def clear_logs(self):
        """清空日志"""
        try:
            self.log_buffer.clear()
            self.log_display.clear()
            self.update_log_count()
            self.add_log("INFO", "日志已清空")
            
        except Exception as e:
            self.logger.error(f"清空日志失败: {e}")
    
    @pyqtSlot()
    def export_logs(self):
        """导出日志"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            
            # 选择保存文件
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出日志", 
                f"system_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;所有文件 (*)"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("系统日志导出\n")
                    f.write(f"导出时间: {datetime.now()}\n")
                    f.write("=" * 80 + "\n\n")
                    
                    for log_entry in self.log_buffer:
                        time_str = log_entry['timestamp'].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                        f.write(f"[{time_str}] [{log_entry['level']:8}] [{log_entry['module']:10}] {log_entry['message']}\n")
                
                self.add_log("INFO", f"日志已导出到: {filename}")
            
        except Exception as e:
            self.logger.error(f"导出日志失败: {e}")
            self.add_log("ERROR", f"日志导出失败: {e}")
    
    def update_log_count(self):
        """更新日志计数"""
        try:
            total_count = len(self.log_buffer)
            
            # 计算当前显示的日志数量
            displayed_count = 0
            for log_entry in self.log_buffer:
                if self.should_display_log(log_entry):
                    displayed_count += 1
            
            if self.level_filter == "ALL":
                self.count_label.setText(f"日志: {total_count}")
            else:
                self.count_label.setText(f"日志: {displayed_count}/{total_count}")
            
        except Exception as e:
            self.logger.error(f"更新日志计数失败: {e}")
    
    @pyqtSlot()
    def cleanup_old_logs(self):
        """清理过期日志"""
        try:
            # 如果日志数量超过限制，自动清理最旧的日志
            if len(self.log_buffer) >= self.max_logs * 0.9:
                # 保留最新的80%日志
                keep_count = int(self.max_logs * 0.8)
                logs_to_keep = list(self.log_buffer)[-keep_count:]
                
                self.log_buffer.clear()
                self.log_buffer.extend(logs_to_keep)
                
                self.refresh_display()
                self.add_log("INFO", f"自动清理旧日志，保留最新 {keep_count} 条")
            
        except Exception as e:
            self.logger.error(f"清理旧日志失败: {e}")
    
    def add_system_event(self, level: str, module: str, message: str):
        """添加系统事件日志"""
        self.add_log(level, message, module)
    
    def get_log_statistics(self) -> dict:
        """获取日志统计信息"""
        try:
            stats = {
                'total': len(self.log_buffer),
                'debug': 0,
                'info': 0,
                'warning': 0,
                'error': 0,
                'critical': 0
            }
            
            for log_entry in self.log_buffer:
                level = log_entry['level'].lower()
                if level in stats:
                    stats[level] += 1
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取日志统计失败: {e}")
            return {}
    
    def search_logs(self, keyword: str) -> list:
        """搜索日志"""
        try:
            results = []
            keyword_lower = keyword.lower()
            
            for log_entry in self.log_buffer:
                if (keyword_lower in log_entry['message'].lower() or 
                    keyword_lower in log_entry['module'].lower()):
                    results.append(log_entry)
            
            return results
            
        except Exception as e:
            self.logger.error(f"搜索日志失败: {e}")
            return []
