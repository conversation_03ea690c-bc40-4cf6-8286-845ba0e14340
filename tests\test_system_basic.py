"""
系统基础功能测试
"""

import unittest
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructure.database.database_manager import DatabaseManager
from src.infrastructure.config.config_manager import ConfigManager
from src.business_logic.auth.user_auth_manager import UserAuthManager
from src.business_logic.classifier.plsa_classifier import PLSAClassifier
from src.business_logic.orchestrator.pso_orchestrator import PSOOrchestrator


class TestSystemBasic(unittest.TestCase):
    """系统基础功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_db_path = "test_system.db"
        
        # 清理测试数据库
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def tearDown(self):
        """测试后清理"""
        # 清理测试数据库
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def test_config_manager(self):
        """测试配置管理器"""
        try:
            config_manager = ConfigManager()
            
            # 测试获取配置
            db_config = config_manager.get_database_config()
            self.assertIsNotNone(db_config)
            
            plsa_config = config_manager.get_plsa_config()
            self.assertIsNotNone(plsa_config)
            
            pso_config = config_manager.get_pso_config()
            self.assertIsNotNone(pso_config)
            
            print("✓ 配置管理器测试通过")
            
        except Exception as e:
            self.fail(f"配置管理器测试失败: {e}")
    
    def test_database_manager(self):
        """测试数据库管理器"""
        try:
            db_manager = DatabaseManager(self.test_db_path)
            
            # 测试数据库初始化
            self.assertTrue(os.path.exists(self.test_db_path))
            
            # 测试用户管理
            user_data = {
                'username': 'test_user',
                'password_hash': 'test_hash',
                'role': 'OPERATOR',
                'email': '<EMAIL>'
            }
            
            success = db_manager.create_user(user_data)
            self.assertTrue(success)
            
            # 测试获取用户
            user = db_manager.get_user('test_user')
            self.assertIsNotNone(user)
            self.assertEqual(user['username'], 'test_user')
            
            # 关闭数据库
            db_manager.close()
            
            print("✓ 数据库管理器测试通过")
            
        except Exception as e:
            self.fail(f"数据库管理器测试失败: {e}")
    
    def test_auth_manager(self):
        """测试认证管理器"""
        try:
            db_manager = DatabaseManager(self.test_db_path)
            auth_manager = UserAuthManager(db_manager)
            
            # 测试用户注册
            success = auth_manager.register_user(
                'test_user', 'test_password', '<EMAIL>', 'OPERATOR'
            )
            self.assertTrue(success)
            
            # 测试用户登录
            session_id = auth_manager.login('test_user', 'test_password')
            self.assertIsNotNone(session_id)
            
            # 测试会话验证
            is_valid = auth_manager.validate_session(session_id)
            self.assertTrue(is_valid)
            
            # 测试用户注销
            success = auth_manager.logout(session_id)
            self.assertTrue(success)
            
            # 关闭数据库
            db_manager.close()
            
            print("✓ 认证管理器测试通过")
            
        except Exception as e:
            self.fail(f"认证管理器测试失败: {e}")
    
    def test_plsa_classifier(self):
        """测试PLSA分类器"""
        try:
            classifier = PLSAClassifier()
            
            # 测试分类器初始化
            self.assertIsNotNone(classifier)
            
            # 创建模拟流量数据
            flow_data = {
                'src_ip': '*************',
                'dst_ip': '*************',
                'src_port': 502,  # Modbus端口
                'dst_port': 502,
                'protocol': 'TCP',
                'packet_size': 64,
                'payload': b'\x00\x01\x00\x00\x00\x06\x01\x03\x00\x00\x00\x01'  # Modbus数据
            }
            
            # 测试特征提取
            features = classifier.extract_features(flow_data)
            self.assertIsNotNone(features)
            self.assertIsInstance(features, dict)
            
            # 测试FEC计算
            fec_score = classifier.calculate_fec(flow_data)
            self.assertIsInstance(fec_score, float)
            self.assertGreaterEqual(fec_score, 0.0)
            self.assertLessEqual(fec_score, 1.0)
            
            print("✓ PLSA分类器测试通过")
            
        except Exception as e:
            self.fail(f"PLSA分类器测试失败: {e}")
    
    def test_pso_orchestrator(self):
        """测试PSO编排器"""
        try:
            orchestrator = PSOOrchestrator()
            
            # 测试编排器初始化
            self.assertIsNotNone(orchestrator)
            
            # 创建模拟网络拓扑
            topology = {
                'nodes': ['A', 'B', 'C', 'D'],
                'edges': [
                    ('A', 'B', {'latency': 10, 'bandwidth': 100}),
                    ('B', 'C', {'latency': 15, 'bandwidth': 80}),
                    ('C', 'D', {'latency': 12, 'bandwidth': 90}),
                    ('A', 'D', {'latency': 25, 'bandwidth': 60})
                ]
            }
            
            orchestrator.update_topology(topology)
            
            # 创建模拟流量需求
            flow_request = {
                'flow_id': 'test_flow_001',
                'src_node': 'A',
                'dst_node': 'D',
                'qos_requirements': {
                    'latency': 30.0,
                    'bandwidth': 50.0,
                    'packet_loss': 1.0
                }
            }
            
            # 测试路径优化
            result = orchestrator.optimize_path(flow_request)
            self.assertIsNotNone(result)
            self.assertIn('path', result)
            self.assertIn('total_latency', result)
            
            print("✓ PSO编排器测试通过")
            
        except Exception as e:
            self.fail(f"PSO编排器测试失败: {e}")
    
    def test_import_dependencies(self):
        """测试依赖包导入"""
        try:
            # 测试核心依赖
            import PyQt5
            import numpy
            import sklearn
            import matplotlib
            
            # 测试可选依赖
            try:
                import torch
                print("✓ PyTorch可用")
            except ImportError:
                print("⚠ PyTorch不可用，GNN功能将受限")
            
            try:
                import scapy
                print("✓ Scapy可用")
            except ImportError:
                print("⚠ Scapy不可用，网络抓包功能将受限")
            
            print("✓ 依赖包导入测试通过")
            
        except ImportError as e:
            self.fail(f"依赖包导入失败: {e}")


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    def test_system_workflow(self):
        """测试系统工作流程"""
        try:
            # 1. 初始化配置
            config_manager = ConfigManager()
            
            # 2. 初始化数据库
            db_manager = DatabaseManager("test_integration.db")
            
            # 3. 初始化认证
            auth_manager = UserAuthManager(db_manager)
            
            # 4. 创建测试用户
            success = auth_manager.register_user(
                'admin', 'admin123', '<EMAIL>', 'ADMIN'
            )
            self.assertTrue(success)
            
            # 5. 用户登录
            session_id = auth_manager.login('admin', 'admin123')
            self.assertIsNotNone(session_id)
            
            # 6. 初始化分类器
            classifier = PLSAClassifier()
            
            # 7. 初始化编排器
            orchestrator = PSOOrchestrator()
            
            # 8. 模拟流量处理
            flow_data = {
                'src_ip': '************',
                'dst_ip': '************',
                'src_port': 502,
                'dst_port': 502,
                'protocol': 'TCP',
                'packet_size': 128
            }
            
            # 分类流量
            features = classifier.extract_features(flow_data)
            self.assertIsNotNone(features)
            
            # 9. 清理
            auth_manager.logout(session_id)
            db_manager.close()
            
            # 删除测试数据库
            if os.path.exists("test_integration.db"):
                os.remove("test_integration.db")
            
            print("✓ 系统集成测试通过")
            
        except Exception as e:
            self.fail(f"系统集成测试失败: {e}")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
